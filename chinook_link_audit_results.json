{"summary": {"total_files": 76, "total_links": 1395, "internal_links": 367, "anchor_links": 1014, "external_links": 14, "broken_internal": 26, "broken_anchors": 0}, "results": [{"file": "000-chinook-index.md", "total_links": 110, "internal_links": 50, "anchor_links": 55, "external_links": 5, "broken_links": [], "working_links": [{"text": "1. Chinook Database Laravel Implementation Guide", "url": "#1-chinook-database-laravel-implementation-guide", "type": "anchor", "status": "Anchor found: 1. Chinook Database Laravel Implementation Guide → #1-chinook-database-laravel-implementation-guide"}, {"text": "✅ Greenfield Single Taxonomy System Implementation", "url": "#greenfield-single-taxonomy-system-implementation", "type": "anchor", "status": "Anchor found: ✅ Greenfield Single Taxonomy System Implementation → #greenfield-single-taxonomy-system-implementation"}, {"text": "1.1. Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1. Table of Contents → #11-table-of-contents"}, {"text": "1.2. Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2. <PERSON><PERSON> → #12-overview"}, {"text": "1.2.1. Enterprise Features", "url": "#121-enterprise-features", "type": "anchor", "status": "Anchor found: 1.2.1. Enterprise Features → #121-enterprise-features"}, {"text": "1.2.2. Key Architectural Changes", "url": "#122-key-architectural-changes", "type": "anchor", "status": "Anchor found: 1.2.2. Key Architectural Changes → #122-key-architectural-changes"}, {"text": "1.3. Getting Started", "url": "#13-getting-started", "type": "anchor", "status": "Anchor found: 1.3. Getting Started → #13-getting-started"}, {"text": "1.3.1. Prerequisites", "url": "#131-prerequisites", "type": "anchor", "status": "Anchor found: 1.3.1. Prerequisites → #131-prerequisites"}, {"text": "1.3.2. Quick Start", "url": "#132-quick-start", "type": "anchor", "status": "Anchor found: 1.3.2. Quick Start → #132-quick-start"}, {"text": "1.3.3. Implementation Roadmap", "url": "#133-implementation-roadmap", "type": "anchor", "status": "Anchor found: 1.3.3. Implementation Roadmap → #133-implementation-roadmap"}, {"text": "1.4. Database Schema Overview", "url": "#14-database-schema-overview", "type": "anchor", "status": "Anchor found: 1.4. Database Schema Overview → #14-database-schema-overview"}, {"text": "1.4.1. Database Schema Diagram", "url": "#141-database-schema-diagram", "type": "anchor", "status": "Anchor found: 1.4.1. Database Schema Diagram → #141-database-schema-diagram"}, {"text": "1.4.2. Core Music Data", "url": "#142-core-music-data", "type": "anchor", "status": "Anchor found: 1.4.2. Core Music Data → #142-core-music-data"}, {"text": "1.4.3. RBAC and Authorization Tables", "url": "#143-rbac-and-authorization-tables", "type": "anchor", "status": "Anchor found: 1.4.3. RBAC and Authorization Tables → #143-rbac-and-authorization-tables"}, {"text": "1.4.4. Customer Management", "url": "#144-customer-management", "type": "anchor", "status": "Anchor found: 1.4.4. Customer Management → #144-customer-management"}, {"text": "1.4.5. Sales System", "url": "#145-sales-system", "type": "anchor", "status": "Anchor found: 1.4.5. Sales System → #145-sales-system"}, {"text": "1.4.6. Playlist System", "url": "#146-playlist-system", "type": "anchor", "status": "Anchor found: 1.4.6. Playlist System → #146-playlist-system"}, {"text": "2. Core Database Implementation", "url": "#2-core-database-implementation", "type": "anchor", "status": "Anchor found: 2. Core Database Implementation → #2-core-database-implementation"}, {"text": "2.1. Chinook Models Guide", "url": "#21-chinook-models-guide", "type": "anchor", "status": "Anchor found: 2.1. Chinook Models Guide → #21-chinook-models-guide"}, {"text": "2.2. <PERSON><PERSON>s Guide", "url": "#22-chinook-migrations-guide", "type": "anchor", "status": "Anchor found: 2.2. Chinook Migrations Guide → #22-chinook-migrations-guide"}, {"text": "2.3. Chinook Factories Guide", "url": "#23-chinook-factories-guide", "type": "anchor", "status": "Anchor found: 2.3. Chinook Factories Guide → #23-chinook-factories-guide"}, {"text": "2.4. <PERSON><PERSON> Seeders Guide", "url": "#24-chinook-seeders-guide", "type": "anchor", "status": "Anchor found: 2.4. <PERSON><PERSON> Seeders Guide → #24-chinook-seeders-guide"}, {"text": "2.5. Chinook Advanced Features Guide", "url": "#25-chinook-advanced-features-guide", "type": "anchor", "status": "Anchor found: 2.5. Chinook Advanced Features Guide → #25-chinook-advanced-features-guide"}, {"text": "2.6. Chinook Media Library Guide", "url": "#26-chinook-media-library-guide", "type": "anchor", "status": "Anchor found: 2.6. Chinook Media Library Guide → #26-chinook-media-library-guide"}, {"text": "2.7. Chinook Hierarchy Comparison Guide", "url": "#27-chinook-hierarchy-comparison-guide", "type": "anchor", "status": "An<PERSON> found: 2.7. Chinook Hierarchy Comparison Guide → #27-chinook-hierarchy-comparison-guide"}, {"text": "3. Filament 4 Admin Panel Implementation", "url": "#3-filament-4-admin-panel-implementation", "type": "anchor", "status": "Anchor found: 3. Filament 4 Admin Panel Implementation → #3-filament-4-admin-panel-implementation"}, {"text": "3.1. Panel Setup \\& Configuration", "url": "#31-panel-setup--configuration", "type": "anchor", "status": "Anchor found: 3.1. Panel Setup & Configuration → #31-panel-setup--configuration"}, {"text": "3.2. Model Standards \\& Architecture", "url": "#32-model-standards--architecture", "type": "anchor", "status": "Anchor found: 3.2. Model Standards & Architecture → #32-model-standards--architecture"}, {"text": "3.3. Resource Development", "url": "#33-resource-development", "type": "anchor", "status": "<PERSON><PERSON> found: 3.3. Resource Development → #33-resource-development"}, {"text": "3.4. Advanced Features \\& Widgets", "url": "#34-advanced-features--widgets", "type": "anchor", "status": "Anchor found: 3.4. Advanced Features & Widgets → #34-advanced-features--widgets"}, {"text": "4. Frontend Development", "url": "#4-frontend-development", "type": "anchor", "status": "Anchor found: 4. Frontend Development → #4-frontend-development"}, {"text": "4.1. Frontend Architecture \\& Patterns", "url": "#41-frontend-architecture--patterns", "type": "anchor", "status": "Anchor found: 4.1. Frontend Architecture & Patterns → #41-frontend-architecture--patterns"}, {"text": "4.2. Livewire/Volt Integration", "url": "#42-livewirevolt-integration", "type": "anchor", "status": "Anchor found: 4.2. Livewire/Volt Integration → #42-livewirevolt-integration"}, {"text": "4.3. Performance \\& Accessibility", "url": "#43-performance--accessibility", "type": "anchor", "status": "Anchor found: 4.3. Performance & Accessibility → #43-performance--accessibility"}, {"text": "5. <PERSON><PERSON> Package Integration", "url": "#5-laravel-package-integration", "type": "anchor", "status": "Anchor found: 5. Laravel Package Integration → #5-laravel-package-integration"}, {"text": "5.1. Essential Package Implementations", "url": "#51-essential-package-implementations", "type": "anchor", "status": "Anchor found: 5.1. Essential Package Implementations → #51-essential-package-implementations"}, {"text": "6. Performance Optimization", "url": "#6-performance-optimization", "type": "anchor", "status": "Anchor found: 6. Performance Optimization → #6-performance-optimization"}, {"text": "7. Testing \\& Quality Assurance", "url": "#7-testing--quality-assurance", "type": "anchor", "status": "Anchor found: 7. Testing & Quality Assurance → #7-testing--quality-assurance"}, {"text": "7.1. Core Testing Framework", "url": "#71-core-testing-framework", "type": "anchor", "status": "<PERSON><PERSON> found: 7.1. Core Testing Framework → #71-core-testing-framework"}, {"text": "7.2. Specialized Testing", "url": "#72-specialized-testing", "type": "anchor", "status": "Anchor found: 7.2. Specialized Testing → #72-specialized-testing"}, {"text": "7.3. Testing Infrastructure", "url": "#73-testing-infrastructure", "type": "anchor", "status": "An<PERSON> found: 7.3. Testing Infrastructure → #73-testing-infrastructure"}, {"text": "8. Documentation Standards", "url": "#8-documentation-standards", "type": "anchor", "status": "Anchor found: 8. Documentation Standards → #8-documentation-standards"}, {"text": "8.1. Content Standards", "url": "#81-content-standards", "type": "anchor", "status": "Anchor found: 8.1. Content Standards → #81-content-standards"}, {"text": "8.2. Technical Standards", "url": "#82-technical-standards", "type": "anchor", "status": "Anchor found: 8.2. Technical Standards → #82-technical-standards"}, {"text": "9. Implementation Checklist", "url": "#9-implementation-checklist", "type": "anchor", "status": "Anchor found: 9. Implementation Checklist → #9-implementation-checklist"}, {"text": "9.1. Core Features", "url": "#91-core-features", "type": "anchor", "status": "Anchor found: 9.1. Core Features → #91-core-features"}, {"text": "9.2. Database \\& Data", "url": "#92-database--data", "type": "anchor", "status": "Anchor found: 9.2. Database & Data → #92-database--data"}, {"text": "9.3. Security \\& Performance", "url": "#93-security--performance", "type": "anchor", "status": "Anchor found: 9.3. Security & Performance → #93-security--performance"}, {"text": "9.4. <PERSON><PERSON><PERSON> Admin Panel", "url": "#94-filament-admin-panel", "type": "anchor", "status": "Anchor found: 9.4. Filament Admin Panel → #94-filament-admin-panel"}, {"text": "9.5. Frontend Implementation", "url": "#95-frontend-implementation", "type": "anchor", "status": "Anchor found: 9.5. Frontend Implementation → #95-frontend-implementation"}, {"text": "10. Key Resources", "url": "#10-key-resources", "type": "anchor", "status": "Anchor found: 10. Key Resources → #10-key-resources"}, {"text": "10.1. Quick Reference", "url": "#101-quick-reference", "type": "anchor", "status": "Anchor found: 10.1. Quick Reference → #101-quick-reference"}, {"text": "10.2. External Documentation", "url": "#102-external-documentation", "type": "anchor", "status": "Anchor found: 10.2. External Documentation → #102-external-documentation"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation → #navigation"}, {"text": "010-chinook-models-guide.md", "url": "010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "020-chinook-migrations-guide.md", "url": "020-chinook-migrations-guide.md", "type": "internal", "status": "File exists: 020-chinook-migrations-guide.md"}, {"text": "030-chinook-factories-guide.md", "url": "030-chinook-factories-guide.md", "type": "internal", "status": "File exists: 030-chinook-factories-guide.md"}, {"text": "040-chinook-seeders-guide.md", "url": "040-chinook-seeders-guide.md", "type": "internal", "status": "File exists: 040-chinook-seeders-guide.md"}, {"text": "050-chinook-advanced-features-guide.md", "url": "050-chinook-advanced-features-guide.md", "type": "internal", "status": "File exists: 050-chinook-advanced-features-guide.md"}, {"text": "060-chinook-media-library-guide.md", "url": "060-chinook-media-library-guide.md", "type": "internal", "status": "File exists: 060-chinook-media-library-guide.md"}, {"text": "070-chinook-hierarchy-comparison-guide.md", "url": "070-chinook-hierarchy-comparison-guide.md", "type": "internal", "status": "File exists: 070-chinook-hierarchy-comparison-guide.md"}, {"text": "filament/000-filament-index.md", "url": "filament/000-filament-index.md", "type": "internal", "status": "File exists: filament/000-filament-index.md"}, {"text": "filament/models/000-models-index.md", "url": "filament/models/000-models-index.md", "type": "internal", "status": "File exists: filament/models/000-models-index.md"}, {"text": "filament/resources/000-resources-index.md", "url": "filament/resources/000-resources-index.md", "type": "internal", "status": "File exists: filament/resources/000-resources-index.md"}, {"text": "Resources Index", "url": "filament/resources/000-resources-index.md", "type": "internal", "status": "File exists: filament/resources/000-resources-index.md"}, {"text": "Tracks Resource", "url": "filament/resources/030-tracks-resource.md", "type": "internal", "status": "File exists: filament/resources/030-tracks-resource.md"}, {"text": "Taxonomy Resource", "url": "filament/resources/040-taxonomy-resource.md", "type": "internal", "status": "File exists: filament/resources/040-taxonomy-resource.md"}, {"text": "filament/features/000-features-index.md", "url": "filament/features/000-features-index.md", "type": "internal", "status": "File exists: filament/features/000-features-index.md"}, {"text": "frontend/100-frontend-architecture-overview.md", "url": "frontend/100-frontend-architecture-overview.md", "type": "internal", "status": "File exists: frontend/100-frontend-architecture-overview.md"}, {"text": "frontend/160-livewire-volt-integration-guide.md", "url": "frontend/160-livewire-volt-integration-guide.md", "type": "internal", "status": "File exists: frontend/160-livewire-volt-integration-guide.md"}, {"text": "frontend/140-accessibility-wcag-guide.md", "url": "frontend/140-accessibility-wcag-guide.md", "type": "internal", "status": "File exists: frontend/140-accessibility-wcag-guide.md"}, {"text": "<PERSON><PERSON>up Guide", "url": "packages/010-laravel-backup-guide.md", "type": "internal", "status": "File exists: packages/010-laravel-backup-guide.md"}, {"text": "<PERSON><PERSON>", "url": "packages/020-laravel-pulse-guide.md", "type": "internal", "status": "File exists: packages/020-laravel-pulse-guide.md"}, {"text": "Laravel Telescope Guide", "url": "packages/030-laravel-telescope-guide.md", "type": "internal", "status": "File exists: packages/030-laravel-telescope-guide.md"}, {"text": "<PERSON>vel Octane FrankenPHP Guide", "url": "packages/040-laravel-octane-frankenphp-guide.md", "type": "internal", "status": "File exists: packages/040-laravel-octane-frankenphp-guide.md"}, {"text": "Laravel Horizon Guide", "url": "packages/050-laravel-horizon-guide.md", "type": "internal", "status": "File exists: packages/050-laravel-horizon-guide.md"}, {"text": "Laravel Data Guide", "url": "packages/060-laravel-data-guide.md", "type": "internal", "status": "File exists: packages/060-laravel-data-guide.md"}, {"text": "<PERSON><PERSON>", "url": "packages/070-laravel-fractal-guide.md", "type": "internal", "status": "File exists: packages/070-laravel-fractal-guide.md"}, {"text": "Laravel Sanctum Guide", "url": "packages/080-laravel-sanctum-guide.md", "type": "internal", "status": "File exists: packages/080-laravel-sanctum-guide.md"}, {"text": "Laravel WorkOS Guide", "url": "packages/090-laravel-workos-guide.md", "type": "internal", "status": "File exists: packages/090-laravel-workos-guide.md"}, {"text": "⚠️ DEPRECATED: <PERSON><PERSON>s Guide", "url": "packages/100-spatie-tags-guide.md", "type": "internal", "status": "File exists: packages/100-spatie-tags-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "packages/110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Spatie Media Library Guide", "url": "packages/120-spatie-media-library-guide.md", "type": "internal", "status": "File exists: packages/120-spatie-media-library-guide.md"}, {"text": "Spatie Permission Guide", "url": "packages/140-spatie-permission-guide.md", "type": "internal", "status": "File exists: packages/140-spatie-permission-guide.md"}, {"text": "Spatie Comments Guide", "url": "packages/150-spatie-comments-guide.md", "type": "internal", "status": "File exists: packages/150-spatie-comments-guide.md"}, {"text": "Spatie Activitylog Guide", "url": "packages/160-spatie-activitylog-guide.md", "type": "internal", "status": "File exists: packages/160-spatie-activitylog-guide.md"}, {"text": "<PERSON><PERSON> Settings Guide", "url": "packages/180-spatie-laravel-settings-guide.md", "type": "internal", "status": "File exists: packages/180-spatie-laravel-settings-guide.md"}, {"text": "<PERSON><PERSON> Query Builder Guide", "url": "packages/200-spatie-laravel-query-builder-guide.md", "type": "internal", "status": "File exists: packages/200-spatie-laravel-query-builder-guide.md"}, {"text": "<PERSON><PERSON> Translatable Guide", "url": "packages/220-spatie-laravel-translatable-guide.md", "type": "internal", "status": "File exists: packages/220-spatie-laravel-translatable-guide.md"}, {"text": "Laravel Folio Guide", "url": "packages/170-laravel-folio-guide.md", "type": "internal", "status": "File exists: packages/170-laravel-folio-guide.md"}, {"text": "Nnjeim World Guide", "url": "packages/190-nnjeim-world-guide.md", "type": "internal", "status": "File exists: packages/190-nnjeim-world-guide.md"}, {"text": "Laravel Optimize Database Guide", "url": "packages/210-laravel-optimize-database-guide.md", "type": "internal", "status": "File exists: packages/210-laravel-optimize-database-guide.md"}, {"text": "performance/000-performance-index.md", "url": "performance/000-performance-index.md", "type": "internal", "status": "File exists: performance/000-performance-index.md"}, {"text": "testing/000-testing-index.md", "url": "testing/000-testing-index.md", "type": "internal", "status": "File exists: testing/000-testing-index.md"}, {"text": "Testing Index Overview", "url": "testing/index/000-index-overview.md", "type": "internal", "status": "File exists: testing/index/000-index-overview.md"}, {"text": "Trait Testing Guide", "url": "testing/070-trait-testing-guide.md", "type": "internal", "status": "File exists: testing/070-trait-testing-guide.md"}, {"text": "Testing Diagrams", "url": "testing/diagrams/000-diagrams-index.md", "type": "internal", "status": "File exists: testing/diagrams/000-diagrams-index.md"}, {"text": "Quality Assurance", "url": "testing/quality/000-quality-index.md", "type": "internal", "status": "File exists: testing/quality/000-quality-index.md"}, {"text": "Testing Documentation", "url": "testing/000-testing-index.md", "type": "internal", "status": "File exists: testing/000-testing-index.md"}, {"text": "Essential Packages Index", "url": "packages/000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "packages/110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Spatie Permission Guide", "url": "packages/140-spatie-permission-guide.md", "type": "internal", "status": "File exists: packages/140-spatie-permission-guide.md"}, {"text": "<PERSON><PERSON>", "url": "packages/020-laravel-pulse-guide.md", "type": "internal", "status": "File exists: packages/020-laravel-pulse-guide.md"}, {"text": "Testing Documentation", "url": "testing/000-testing-index.md", "type": "internal", "status": "File exists: testing/000-testing-index.md"}, {"text": "Laravel 12 Documentation", "url": "https://laravel.com/docs", "type": "external", "status": "External link (not validated)"}, {"text": "Filament 4 Documentation", "url": "https://filamentphp.com/docs", "type": "external", "status": "External link (not validated)"}, {"text": "Aliziodev Laravel Taxonomy", "url": "https://github.com/aliziodev/laravel-taxonomy", "type": "external", "status": "External link (not validated)"}, {"text": "<PERSON><PERSON>", "url": "https://spatie.be/docs/laravel-permission", "type": "external", "status": "External link (not validated)"}, {"text": "WCAG 2.1 Guidelines", "url": "https://www.w3.org/WAI/WCAG21/quickref/", "type": "external", "status": "External link (not validated)"}, {"text": "Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1. Table of Contents → #11-table-of-contents"}]}, {"file": "010-chinook-models-guide.md", "total_links": 33, "internal_links": 2, "anchor_links": 31, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2. Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2. <PERSON><PERSON> → #12-overview"}, {"text": "1.2.1. Modern Laravel 12 Features", "url": "#121-modern-laravel-12-features", "type": "anchor", "status": "Anchor found: 1.2.1. Modern Laravel 12 Features → #121-modern-laravel-12-features"}, {"text": "1.2.2. Database Schema Overview", "url": "#122-database-schema-overview", "type": "anchor", "status": "Anchor found: 1.2.2. Database Schema Overview → #122-database-schema-overview"}, {"text": "1.2.3. Required Packages", "url": "#123-required-packages", "type": "anchor", "status": "Anchor found: 1.2.3. Required Packages → #123-required-packages"}, {"text": "1.3. Model Architecture", "url": "#13-model-architecture", "type": "anchor", "status": "<PERSON><PERSON> found: 1.3. Model Architecture → #13-model-architecture"}, {"text": "1.3.1. Base Model Traits", "url": "#131-base-model-traits", "type": "anchor", "status": "An<PERSON> found: 1.3.1. Base Model Traits → #131-base-model-traits"}, {"text": "1.3.2. Taxonomy Integration", "url": "#132-taxonomy-integration", "type": "anchor", "status": "<PERSON><PERSON> found: 1.3.2. Taxonomy Integration → #132-taxonomy-integration"}, {"text": "1.3.3. Performance Considerations", "url": "#133-performance-considerations", "type": "anchor", "status": "Anchor found: 1.3.3. Performance Considerations → #133-performance-considerations"}, {"text": "1.4. Core Music Models", "url": "#14-core-music-models", "type": "anchor", "status": "Anchor found: 1.4. Core Music Models → #14-core-music-models"}, {"text": "1.4.1. ChinookArtist Model", "url": "#141-chinookartist-model", "type": "anchor", "status": "Anchor found: 1.4.1. ChinookArtist Model → #141-chinookartist-model"}, {"text": "1.4.2. ChinookAlbum Model", "url": "#142-chinookalbum-model", "type": "anchor", "status": "Anchor found: 1.4.2. ChinookAlbum Model → #142-chinookalbum-model"}, {"text": "1.4.3. ChinookTrack Model", "url": "#143-chinooktrack-model", "type": "anchor", "status": "Anchor found: 1.4.3. ChinookTrack Model → #143-chinooktrack-model"}, {"text": "1.4.4. ChinookMediaType Model", "url": "#144-chinookmediatype-model", "type": "anchor", "status": "Anchor found: 1.4.4. ChinookMediaType Model → #144-chinookmediatype-model"}, {"text": "2. Customer & Employee Models", "url": "#2-customer--employee-models", "type": "anchor", "status": "Anchor found: 2. Customer & Employee Models → #2-customer--employee-models"}, {"text": "2.1. <PERSON><PERSON><PERSON>ust<PERSON> Model", "url": "#21-chinookcus<PERSON>er-model", "type": "anchor", "status": "Anchor found: 2.1. ChinookCustomer Model → #21-chinookcustomer-model"}, {"text": "2.2. ChinookEmployee Model", "url": "#22-chinookemployee-model", "type": "anchor", "status": "Anchor found: 2.2. ChinookEmployee Model → #22-chinookemployee-model"}, {"text": "3. Sales Models", "url": "#3-sales-models", "type": "anchor", "status": "Anchor found: 3. Sales Models → #3-sales-models"}, {"text": "3.1. ChinookInvoice Model", "url": "#31-chinookinvoice-model", "type": "anchor", "status": "Anchor found: 3.1. ChinookInvoice Model → #31-chinookinvoice-model"}, {"text": "3.2. ChinookInvoiceLine Model", "url": "#32-chinookinvoiceline-model", "type": "anchor", "status": "Anchor found: 3.2. ChinookInvoiceLine Model → #32-chinookinvoiceline-model"}, {"text": "4. Playlist Models", "url": "#4-playlist-models", "type": "anchor", "status": "Anchor found: 4. Playlist Models → #4-playlist-models"}, {"text": "4.1. ChinookPlaylist Model", "url": "#41-chinookplaylist-model", "type": "anchor", "status": "Anchor found: 4.1. ChinookPlaylist Model → #41-chinookplaylist-model"}, {"text": "5. Taxonomy Models", "url": "#5-taxonomy-models", "type": "anchor", "status": "An<PERSON> found: 5. Taxonomy Models → #5-taxonomy-models"}, {"text": "5.1. ChinookGenre Model", "url": "#51-chinookgenre-model", "type": "anchor", "status": "Anchor found: 5.1. ChinookGenre Model → #51-chinookgenre-model"}, {"text": "6. Model Relationships Summary", "url": "#6-model-relationships-summary", "type": "anchor", "status": "An<PERSON> found: 6. Model Relationships Summary → #6-model-relationships-summary"}, {"text": "6.1. Core Music Relationships", "url": "#61-core-music-relationships", "type": "anchor", "status": "Anchor found: 6.1. Core Music Relationships → #61-core-music-relationships"}, {"text": "6.2. Taxonomy Relationships", "url": "#62-taxonomy-relationships", "type": "anchor", "status": "<PERSON><PERSON> found: 6.2. Taxonomy Relationships → #62-taxonomy-relationships"}, {"text": "6.3. RBAC Relationships", "url": "#63-rbac-relationships", "type": "anchor", "status": "Anchor found: 6.3. RBAC Relationships → #63-rbac-relationships"}, {"text": "7. Testing & Validation", "url": "#7-testing--validation", "type": "anchor", "status": "Anchor found: 7. Testing & Validation → #7-testing--validation"}, {"text": "7.1. Model Testing", "url": "#71-model-testing", "type": "anchor", "status": "<PERSON><PERSON> found: 7.1. Model Testing → #71-model-testing"}, {"text": "7.2. Relationship Testing", "url": "#72-relationship-testing", "type": "anchor", "status": "<PERSON><PERSON> found: 7.2. Relationship Testing → #72-relationship-testing"}, {"text": "000-chinook-index.md", "url": "000-chinook-index.md", "type": "internal", "status": "File exists: 000-chinook-index.md"}, {"text": "Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1. Table of Contents → #11-table-of-contents"}, {"text": "020-chinook-migrations-guide.md", "url": "020-chinook-migrations-guide.md", "type": "internal", "status": "File exists: 020-chinook-migrations-guide.md"}]}, {"file": "020-chinook-migrations-guide.md", "total_links": 22, "internal_links": 5, "anchor_links": 17, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2. Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2. <PERSON><PERSON> → #12-overview"}, {"text": "1.2.1. Modern Laravel 12 Features", "url": "#121-modern-laravel-12-features-included", "type": "anchor", "status": "Anchor found: 1.2.1. Modern Laravel 12 Features Included → #121-modern-laravel-12-features-included"}, {"text": "1.2.2. Greenfield Implementation Strategy", "url": "#122-greenfield-implementation-strategy", "type": "anchor", "status": "Anchor found: 1.2.2. Greenfield Implementation Strategy → #122-greenfield-implementation-strategy"}, {"text": "2. Prerequisites", "url": "#2-prerequisites", "type": "anchor", "status": "Anchor found: 2. Prerequisites → #2-prerequisites"}, {"text": "2.1. Required Packages", "url": "#21-required-packages", "type": "anchor", "status": "Anchor found: 2.1. Required Packages → #21-required-packages"}, {"text": "2.2. Package Installation", "url": "#22-package-installation", "type": "anchor", "status": "Anchor found: 2.2. Package Installation → #22-package-installation"}, {"text": "3. Schema Creation Order", "url": "#3-schema-creation-order", "type": "anchor", "status": "Anchor found: 3. <PERSON><PERSON>a Creation Order → #3-schema-creation-order"}, {"text": "3.1. Dependency Order", "url": "#31-dependency-order", "type": "anchor", "status": "Anchor found: 3.1. Dependency Order → #31-dependency-order"}, {"text": "3.2. Generate Migration Commands", "url": "#32-generate-migration-commands", "type": "anchor", "status": "Anchor found: 3.2. Generate Migration Commands → #32-generate-migration-commands"}, {"text": "4. <PERSON><PERSON><PERSON> Implementations", "url": "#4-schema-implementations", "type": "anchor", "status": "Anchor found: 4. Schema Implementations → #4-schema-implementations"}, {"text": "4.1. Database Schema Overview", "url": "#41-database-schema-overview", "type": "anchor", "status": "Anchor found: 4.1. Database Schema Overview → #41-database-schema-overview"}, {"text": "4.2. Artists Migration", "url": "#42-artists-migration", "type": "anchor", "status": "Anchor found: 4.2. Artists Migration → #42-artists-migration"}, {"text": "4.3. Taxonomy System Migrations", "url": "#43-taxonomy-system-migrations", "type": "anchor", "status": "<PERSON><PERSON> found: 4.3. Taxonomy System Migrations → #43-taxonomy-system-migrations"}, {"text": "5. Modern Laravel Features Summary", "url": "#5-modern-laravel-features-summary", "type": "anchor", "status": "Anchor found: 5. Modern Laravel Features Summary → #5-modern-laravel-features-summary"}, {"text": "6. <PERSON><PERSON> Best Practices", "url": "#6-migration-best-practices", "type": "anchor", "status": "Anchor found: 6. Migration Best Practices → #6-migration-best-practices"}, {"text": "7. Next Steps", "url": "#7-next-steps", "type": "anchor", "status": "Anchor found: 7. Next Steps → #7-next-steps"}, {"text": "Chinook Models Guide", "url": "010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Chinook Seeders Guide", "url": "040-chinook-seeders-guide.md", "type": "internal", "status": "File exists: 040-chinook-seeders-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "packages/110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "010-chinook-models-guide.md", "url": "010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1. Table of Contents → #11-table-of-contents"}, {"text": "030-chinook-factories-guide.md", "url": "030-chinook-factories-guide.md", "type": "internal", "status": "File exists: 030-chinook-factories-guide.md"}]}, {"file": "030-chinook-factories-guide.md", "total_links": 20, "internal_links": 0, "anchor_links": 20, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2. Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2. <PERSON><PERSON> → #12-overview"}, {"text": "1.2.1. Modern Laravel 12 Features", "url": "#121-modern-laravel-12-features", "type": "anchor", "status": "Anchor found: 1.2.1. Modern Laravel 12 Features → #121-modern-laravel-12-features"}, {"text": "1.2.2. Single Taxonomy System Strategy", "url": "#122-single-taxonomy-system-strategy", "type": "anchor", "status": "<PERSON><PERSON> found: 1.2.2. Single Taxonomy System Strategy → #122-single-taxonomy-system-strategy"}, {"text": "2. Factory Architecture", "url": "#2-factory-architecture", "type": "anchor", "status": "Anchor found: 2. Factory Architecture → #2-factory-architecture"}, {"text": "2.1. Base Factory Patterns", "url": "#21-base-factory-patterns", "type": "anchor", "status": "Anchor found: 2.1. Base Factory Patterns → #21-base-factory-patterns"}, {"text": "2.2. Taxonomy Integration", "url": "#22-taxonomy-integration", "type": "anchor", "status": "<PERSON><PERSON> found: 2.2. Taxonomy Integration → #22-taxonomy-integration"}, {"text": "3. Core Music Factories", "url": "#3-core-music-factories", "type": "anchor", "status": "Anchor found: 3. Core Music Factories → #3-core-music-factories"}, {"text": "3.1. Artist Factory", "url": "#31-artist-factory", "type": "anchor", "status": "Anchor found: 3.1. Artist Factory → #31-artist-factory"}, {"text": "3.2. Album Factory", "url": "#32-album-factory", "type": "anchor", "status": "Anchor found: 3.2. Album Factory → #32-album-factory"}, {"text": "3.3. Track Factory", "url": "#33-track-factory", "type": "anchor", "status": "Anchor found: 3.3. Track Factory → #33-track-factory"}, {"text": "4. Taxonomy System Factories", "url": "#4-taxonomy-system-factories", "type": "anchor", "status": "An<PERSON> found: 4. Taxonomy System Factories → #4-taxonomy-system-factories"}, {"text": "4.1. Taxonomy Factory", "url": "#41-taxonomy-factory", "type": "anchor", "status": "Anchor found: 4.1. Taxonomy Factory → #41-taxonomy-factory"}, {"text": "5. Advanced Factory Patterns", "url": "#5-advanced-factory-patterns", "type": "anchor", "status": "Anchor found: 5. Advanced Factory Patterns → #5-advanced-factory-patterns"}, {"text": "5.1. Relationship Factories", "url": "#51-relationship-factories", "type": "anchor", "status": "Anchor found: 5.1. Relationship Factories → #51-relationship-factories"}, {"text": "6. Testing Integration", "url": "#6-testing-integration", "type": "anchor", "status": "<PERSON><PERSON> found: 6. Testing Integration → #6-testing-integration"}, {"text": "6.1. Factory Testing", "url": "#61-factory-testing", "type": "anchor", "status": "Anchor found: 6.1. Factory Testing → #61-factory-testing"}, {"text": "7. Best Practices", "url": "#7-best-practices", "type": "anchor", "status": "Anchor found: 7. Best Practices → #7-best-practices"}, {"text": "7.1. Factory Guidelines", "url": "#71-factory-guidelines", "type": "anchor", "status": "Anchor found: 7.1. Factory Guidelines → #71-factory-guidelines"}, {"text": "5.2. Bulk Data Generation", "url": "#52-bulk-data-generation", "type": "anchor", "status": "An<PERSON> found: 5.2. Bulk Data Generation → #52-bulk-data-generation"}, {"text": "Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1. Table of Contents → #11-table-of-contents"}]}, {"file": "040-chinook-seeders-guide.md", "total_links": 22, "internal_links": 0, "anchor_links": 22, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2. Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2. <PERSON><PERSON> → #12-overview"}, {"text": "1.2.1. Modern Laravel 12 Features", "url": "#121-modern-laravel-12-features", "type": "anchor", "status": "Anchor found: 1.2.1. Modern Laravel 12 Features → #121-modern-laravel-12-features"}, {"text": "1.2.2. Single Taxonomy System Strategy", "url": "#122-single-taxonomy-system-strategy", "type": "anchor", "status": "<PERSON><PERSON> found: 1.2.2. Single Taxonomy System Strategy → #122-single-taxonomy-system-strategy"}, {"text": "2. Prerequisites", "url": "#2-prerequisites", "type": "anchor", "status": "Anchor found: 2. Prerequisites → #2-prerequisites"}, {"text": "2.1. Required Packages", "url": "#21-required-packages", "type": "anchor", "status": "Anchor found: 2.1. Required Packages → #21-required-packages"}, {"text": "2.2. Genre Preservation Strategy", "url": "#22-genre-preservation-strategy", "type": "anchor", "status": "Anchor found: 2.2. Genre Preservation Strategy → #22-genre-preservation-strategy"}, {"text": "3. <PERSON>der Architecture", "url": "#3-seeder-architecture", "type": "anchor", "status": "Anchor found: 3. Seeder Architecture → #3-seeder-architecture"}, {"text": "3.1. Seeding Order", "url": "#31-seeding-order", "type": "anchor", "status": "Anchor found: 3.1. Seeding Order → #31-seeding-order"}, {"text": "3.2. Generate Seeder Commands", "url": "#32-generate-seeder-commands", "type": "anchor", "status": "Anchor found: 3.2. Generate Seeder Commands → #32-generate-seeder-commands"}, {"text": "4. <PERSON>", "url": "#4-core-seeders", "type": "anchor", "status": "Anchor found: 4. <PERSON> → #4-core-seeders"}, {"text": "4.1. <PERSON><PERSON>", "url": "#41-user-seeder", "type": "anchor", "status": "Anchor found: 4.1. <PERSON><PERSON> → #41-user-seeder"}, {"text": "4.2. <PERSON><PERSON> Seeder", "url": "#42-permission-seeder", "type": "anchor", "status": "Anchor found: 4.2. <PERSON><PERSON> → #42-permission-seeder"}, {"text": "4.3. <PERSON>", "url": "#43-role-seeder", "type": "anchor", "status": "Anchor found: 4.3. <PERSON> → #43-role-seeder"}, {"text": "4.4. Taxonomy Seeder", "url": "#44-taxonomy-seeder", "type": "anchor", "status": "Anchor found: 4.4. Taxonomy Seeder → #44-taxonomy-seeder"}, {"text": "4.5. <PERSON>", "url": "#45-artist-seeder", "type": "anchor", "status": "Anchor found: 4.5. <PERSON> <PERSON><PERSON> → #45-artist-seeder"}, {"text": "5. Main Database Seeder", "url": "#5-main-database-seeder", "type": "anchor", "status": "Anchor found: 5. Main Database Seeder → #5-main-database-seeder"}, {"text": "6. Performance Optimization", "url": "#6-performance-optimization", "type": "anchor", "status": "Anchor found: 6. Performance Optimization → #6-performance-optimization"}, {"text": "7. Testing Integration", "url": "#7-testing-integration", "type": "anchor", "status": "An<PERSON> found: 7. Testing Integration → #7-testing-integration"}, {"text": "8. Best Practices", "url": "#8-best-practices", "type": "anchor", "status": "Anchor found: 8. Best Practices → #8-best-practices"}, {"text": "8.1. Seeder Guidelines", "url": "#81-seeder-guidelines", "type": "anchor", "status": "Anchor found: 8.1. Seeder Guidelines → #81-seeder-guidelines"}, {"text": "4.4. Taxonomy Seeder", "url": "#44-taxonomy-seeder", "type": "anchor", "status": "Anchor found: 4.4. Taxonomy Seeder → #44-taxonomy-seeder"}, {"text": "Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1. Table of Contents → #11-table-of-contents"}]}, {"file": "050-chinook-advanced-features-guide.md", "total_links": 6, "internal_links": 2, "anchor_links": 4, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Role-Based Access Control (RBAC) System", "url": "#13-role-based-access-control-rbac-system", "type": "anchor", "status": "Anchor found: 1.3 Role-Based Access Control (RBAC) System → #13-role-based-access-control-rbac-system"}, {"text": "1.4 Taxonomy Management with aliziodev/laravel-taxonomy", "url": "#14-taxonomy-management-with-aliziodevlaravel-taxonomy", "type": "anchor", "status": "Anchor found: 1.4 Taxonomy Management with aliziodev/laravel-taxonomy → #14-taxonomy-management-with-aliziodevlaravel-taxonomy"}, {"text": "1.5 API Authentication and Authorization", "url": "#15-api-authentication-and-authorization", "type": "anchor", "status": "Anchor found: 1.5 API Authentication and Authorization → #15-api-authentication-and-authorization"}, {"text": "README Documentation", "url": "README.md", "type": "internal", "status": "File exists: README.md"}, {"text": "Comprehensive Data Access Guide", "url": "130-comprehensive-data-access-guide.md", "type": "internal", "status": "File exists: 130-comprehensive-data-access-guide.md"}]}, {"file": "060-chinook-media-library-guide.md", "total_links": 8, "internal_links": 2, "anchor_links": 6, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Media Integration", "url": "#2-media-integration", "type": "anchor", "status": "An<PERSON> found: 2. Media Integration → #2-media-integration"}, {"text": "3. Taxonomy Tagging", "url": "#3-taxonomy-tagging", "type": "anchor", "status": "Anchor found: 3. Taxonomy Tagging → #3-taxonomy-tagging"}, {"text": "4. Performance Optimization", "url": "#4-performance-optimization", "type": "anchor", "status": "Anchor found: 4. Performance Optimization → #4-performance-optimization"}, {"text": "5. Security", "url": "#5-security", "type": "anchor", "status": "Anchor found: 5. Security → #5-security"}, {"text": "6. <PERSON>", "url": "#6-navigation", "type": "anchor", "status": "Anchor found: 6. <PERSON> → #6-navigation"}, {"text": "Chinook Advanced Features Guide", "url": "050-chinook-advanced-features-guide.md", "type": "internal", "status": "File exists: 050-chinook-advanced-features-guide.md"}, {"text": "Chinook Hierarchy Comparison Guide", "url": "070-chinook-hierarchy-comparison-guide.md", "type": "internal", "status": "File exists: 070-chinook-hierarchy-comparison-guide.md"}]}, {"file": "070-chinook-hierarchy-comparison-guide.md", "total_links": 8, "internal_links": 2, "anchor_links": 6, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Architecture Benefits", "url": "#2-architecture-benefits", "type": "anchor", "status": "Anchor found: 2. Architecture Benefits → #2-architecture-benefits"}, {"text": "3. Performance Metrics", "url": "#3-performance-metrics", "type": "anchor", "status": "An<PERSON> found: 3. Performance Metrics → #3-performance-metrics"}, {"text": "4. Migration Strategies", "url": "#4-migration-strategies", "type": "anchor", "status": "An<PERSON> found: 4. Migration Strategies → #4-migration-strategies"}, {"text": "5. Best Practices", "url": "#5-best-practices", "type": "anchor", "status": "Anchor found: 5. Best Practices → #5-best-practices"}, {"text": "6. <PERSON>", "url": "#6-navigation", "type": "anchor", "status": "Anchor found: 6. <PERSON> → #6-navigation"}, {"text": "Chinook Media Library Guide", "url": "060-chinook-media-library-guide.md", "type": "internal", "status": "File exists: 060-chinook-media-library-guide.md"}, {"text": "Filament Index", "url": "filament/000-filament-index.md", "type": "internal", "status": "File exists: filament/000-filament-index.md"}]}, {"file": "080-visual-documentation-guide.md", "total_links": 13, "internal_links": 4, "anchor_links": 9, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.1 Overview", "url": "#11-overview", "type": "anchor", "status": "Anchor found: 1.1 Overview → #11-overview"}, {"text": "1.3 WCAG 2.1 AA Compliance Standards", "url": "#13-wcag-21-aa-compliance-standards", "type": "anchor", "status": "Anchor found: 1.3 WCAG 2.1 AA Compliance Standards → #13-wcag-21-aa-compliance-standards"}, {"text": "1.4 Mermaid Diagram Standards", "url": "#14-mermaid-diagram-standards", "type": "anchor", "status": "Anchor found: 1.4 Mermaid Diagram Standards → #14-mermaid-diagram-standards"}, {"text": "1.5 Color Palette Guidelines", "url": "#15-color-palette-guidelines", "type": "anchor", "status": "Anchor found: 1.5 Color Palette Guidelines → #15-color-palette-guidelines"}, {"text": "1.6 Diagram Types and Usage", "url": "#16-diagram-types-and-usage", "type": "anchor", "status": "Anchor found: 1.6 Diagram Types and Usage → #16-diagram-types-and-usage"}, {"text": "1.7 Accessibility Features", "url": "#17-accessibility-features", "type": "anchor", "status": "Anchor found: 1.7 Accessibility Features → #17-accessibility-features"}, {"text": "1.8 Documentation Structure", "url": "#18-documentation-structure", "type": "anchor", "status": "Anchor found: 1.8 Documentation Structure → #18-documentation-structure"}, {"text": "1.9 Quality Assurance", "url": "#19-quality-assurance", "type": "anchor", "status": "Anchor found: 1.9 Quality Assurance → #19-quality-assurance"}, {"text": "1.10 Tools and Integration", "url": "#110-tools-and-integration", "type": "anchor", "status": "An<PERSON> found: 1.10 Tools and Integration → #110-tools-and-integration"}, {"text": "Entity Relationship Diagram showing the core Chinook database schema with Artists, Albums, and Tracks entities integrated with the aliziodev/laravel-taxonomy package. The diagram shows polymorphic relationships between Terms and all Chinook entities through the Termables pivot table, enabling flexible categorization without custom Category models.", "url": "080-visual-documentation-guide.md", "type": "internal", "status": "File exists: 080-visual-documentation-guide.md"}, {"text": "Database diagram", "url": "080-visual-documentation-guide.md", "type": "internal", "status": "File exists: 080-visual-documentation-guide.md"}, {"text": "Relationship Mapping Guide", "url": "090-relationship-mapping.md", "type": "internal", "status": "File exists: 090-relationship-mapping.md"}, {"text": "Hierarchy Comparison Guide", "url": "070-chinook-hierarchy-comparison-guide.md", "type": "internal", "status": "File exists: 070-chinook-hierarchy-comparison-guide.md"}]}, {"file": "090-relationship-mapping.md", "total_links": 10, "internal_links": 2, "anchor_links": 8, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Core Entity Relationships", "url": "#13-core-entity-relationships", "type": "anchor", "status": "An<PERSON> found: 1.3 Core Entity Relationships → #13-core-entity-relationships"}, {"text": "1.4 Taxonomy System Relationships", "url": "#14-taxonomy-system-relationships", "type": "anchor", "status": "<PERSON><PERSON> found: 1.4 Taxonomy System Relationships → #14-taxonomy-system-relationships"}, {"text": "1.5 Hierarchical Relationships", "url": "#15-hierarchical-relationships", "type": "anchor", "status": "Anchor found: 1.5 Hierarchical Relationships → #15-hierarchical-relationships"}, {"text": "1.6 RBAC Relationships", "url": "#16-rbac-relationships", "type": "anchor", "status": "Anchor found: 1.6 RBAC Relationships → #16-rbac-relationships"}, {"text": "1.7 Implementation Examples", "url": "#17-implementation-examples", "type": "anchor", "status": "Anchor found: 1.7 Implementation Examples → #17-implementation-examples"}, {"text": "1.8 Performance Considerations", "url": "#18-performance-considerations", "type": "anchor", "status": "Anchor found: 1.8 Performance Considerations → #18-performance-considerations"}, {"text": "1.9 Testing Relationships", "url": "#19-testing-relationships", "type": "anchor", "status": "<PERSON><PERSON> found: 1.9 Testing Relationships → #19-testing-relationships"}, {"text": "Resource Testing Guide", "url": "100-resource-testing.md", "type": "internal", "status": "File exists: 100-resource-testing.md"}, {"text": "Visual Documentation Guide", "url": "080-visual-documentation-guide.md", "type": "internal", "status": "File exists: 080-visual-documentation-guide.md"}]}, {"file": "100-resource-testing.md", "total_links": 7, "internal_links": 2, "anchor_links": 5, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Testing Framework Setup", "url": "#13-testing-framework-setup", "type": "anchor", "status": "Anchor found: 1.3 Testing Framework Setup → #13-testing-framework-setup"}, {"text": "1.4 Model Testing", "url": "#14-model-testing", "type": "anchor", "status": "<PERSON><PERSON> found: 1.4 Model Testing → #14-model-testing"}, {"text": "1.5 API Resource Testing", "url": "#15-api-resource-testing", "type": "anchor", "status": "Anchor found: 1.5 API Resource Testing → #15-api-resource-testing"}, {"text": "1.6 Filament Resource Testing", "url": "#16-filament-resource-testing", "type": "anchor", "status": "Anchor found: 1.6 Filament Resource Testing → #16-filament-resource-testing"}, {"text": "Authentication Flow Guide", "url": "110-authentication-flow.md", "type": "internal", "status": "File exists: 110-authentication-flow.md"}, {"text": "Relationship Mapping Guide", "url": "090-relationship-mapping.md", "type": "internal", "status": "File exists: 090-relationship-mapping.md"}]}, {"file": "110-authentication-flow.md", "total_links": 10, "internal_links": 2, "anchor_links": 8, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Authentication Architecture", "url": "#13-authentication-architecture", "type": "anchor", "status": "Anchor found: 1.3 Authentication Architecture → #13-authentication-architecture"}, {"text": "1.4 Role-Based Access Control", "url": "#14-role-based-access-control", "type": "anchor", "status": "Anchor found: 1.4 Role-Based Access Control → #14-role-based-access-control"}, {"text": "1.5 Authentication Flow Diagrams", "url": "#15-authentication-flow-diagrams", "type": "anchor", "status": "Anchor found: 1.5 Authentication Flow Diagrams → #15-authentication-flow-diagrams"}, {"text": "1.6 Implementation Details", "url": "#16-implementation-details", "type": "anchor", "status": "Anchor found: 1.6 Implementation Details → #16-implementation-details"}, {"text": "1.7 Security Considerations", "url": "#17-security-considerations", "type": "anchor", "status": "<PERSON><PERSON> found: 1.7 Security Considerations → #17-security-considerations"}, {"text": "1.8 API Authentication", "url": "#18-api-authentication", "type": "anchor", "status": "Anchor found: 1.8 API Authentication → #18-api-authentication"}, {"text": "1.9 Testing Authentication", "url": "#19-testing-authentication", "type": "anchor", "status": "An<PERSON> found: 1.9 Testing Authentication → #19-testing-authentication"}, {"text": "Laravel Query Builder Guide", "url": "120-laravel-query-builder-guide.md", "type": "internal", "status": "File exists: 120-laravel-query-builder-guide.md"}, {"text": "Resource Testing Guide", "url": "100-resource-testing.md", "type": "internal", "status": "File exists: 100-resource-testing.md"}]}, {"file": "120-laravel-query-builder-guide.md", "total_links": 10, "internal_links": 2, "anchor_links": 8, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Installation & Configuration", "url": "#13-installation--configuration", "type": "anchor", "status": "Anchor found: 1.3 Installation & Configuration → #13-installation--configuration"}, {"text": "1.4 Basic Query Building", "url": "#14-basic-query-building", "type": "anchor", "status": "Anchor found: 1.4 Basic Query Building → #14-basic-query-building"}, {"text": "1.5 Advanced Filtering", "url": "#15-advanced-filtering", "type": "anchor", "status": "Anchor found: 1.5 Advanced Filtering → #15-advanced-filtering"}, {"text": "1.6 Sorting & Pagination", "url": "#16-sorting--pagination", "type": "anchor", "status": "Anchor found: 1.6 Sorting & Pagination → #16-sorting--pagination"}, {"text": "1.7 Relationship Queries", "url": "#17-relationship-queries", "type": "anchor", "status": "Anchor found: 1.7 Relationship Queries → #17-relationship-queries"}, {"text": "1.8 Custom Filters", "url": "#18-custom-filters", "type": "anchor", "status": "Anchor found: 1.8 Custom Filters → #18-custom-filters"}, {"text": "1.9 Performance Optimization", "url": "#19-performance-optimization", "type": "anchor", "status": "Anchor found: 1.9 Performance Optimization → #19-performance-optimization"}, {"text": "Comprehensive Data Access Guide", "url": "130-comprehensive-data-access-guide.md", "type": "internal", "status": "File exists: 130-comprehensive-data-access-guide.md"}, {"text": "Authentication Flow Guide", "url": "110-authentication-flow.md", "type": "internal", "status": "File exists: 110-authentication-flow.md"}]}, {"file": "130-comprehensive-data-access-guide.md", "total_links": 10, "internal_links": 2, "anchor_links": 8, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 CLI Data Access", "url": "#13-cli-data-access", "type": "anchor", "status": "Anchor found: 1.3 CLI Data Access → #13-cli-data-access"}, {"text": "1.4 Web Interface Access", "url": "#14-web-interface-access", "type": "anchor", "status": "Anchor found: 1.4 Web Interface Access → #14-web-interface-access"}, {"text": "1.5 API Access", "url": "#15-api-access", "type": "anchor", "status": "Anchor found: 1.5 API Access → #15-api-access"}, {"text": "1.6 Data Export/Import Facilities", "url": "#16-data-exportimport-facilities", "type": "anchor", "status": "Anchor found: 1.6 Data Export/Import Facilities → #16-data-exportimport-facilities"}, {"text": "1.7 Performance Considerations", "url": "#17-performance-considerations", "type": "anchor", "status": "Anchor found: 1.7 Performance Considerations → #17-performance-considerations"}, {"text": "1.8 Security & Authentication", "url": "#18-security--authentication", "type": "anchor", "status": "Anchor found: 1.8 Security & Authentication → #18-security--authentication"}, {"text": "1.9 Best Practices", "url": "#19-best-practices", "type": "anchor", "status": "Anchor found: 1.9 Best Practices → #19-best-practices"}, {"text": "README Documentation", "url": "README.md", "type": "internal", "status": "File exists: README.md"}, {"text": "Laravel Query Builder Guide", "url": "120-laravel-query-builder-guide.md", "type": "internal", "status": "File exists: 120-laravel-query-builder-guide.md"}]}, {"file": "README.md", "total_links": 50, "internal_links": 43, "anchor_links": 7, "external_links": 0, "broken_links": [{"text": "DRIP Implementation Plan", "url": ".ai/tasks/chinook/2025-07-11/DRIP_tasks_2025-07-11.md", "type": "internal", "status": "File not found: .ai/tasks/chinook/2025-07-11/DRIP_tasks_2025-07-11.md"}, {"text": "Panel Setup Guide", "url": "filament/setup/000-setup-index.md", "type": "internal", "status": "File not found: filament/setup/000-setup-index.md"}, {"text": "Testing Guide", "url": "filament/testing/000-testing-index.md", "type": "internal", "status": "File not found: filament/testing/000-testing-index.md"}, {"text": "SQL Schema", "url": "../../database/sqldump/chinook.sql", "type": "internal", "status": "Path outside base directory: /Users/<USER>/Herd/workos-sac/.ai/guides/chinook/../../database/sqldump/chinook.sql"}, {"text": "Documentation Quality Validation", "url": "testing/quality/documentation-quality-validation.md", "type": "internal", "status": "File not found: testing/quality/documentation-quality-validation.md"}], "working_links": [{"text": "1.3 Quick Navigation", "url": "#13-quick-navigation", "type": "anchor", "status": "Anchor found: 1.3 Quick Navigation → #13-quick-navigation"}, {"text": "1.4 Key Features", "url": "#14-key-features", "type": "anchor", "status": "Anchor found: 1.4 Key Features → #14-key-features"}, {"text": "1.5 Getting Started", "url": "#15-getting-started", "type": "anchor", "status": "Anchor found: 1.5 Getting Started → #15-getting-started"}, {"text": "1.6 Architecture Overview", "url": "#16-architecture-overview", "type": "anchor", "status": "Anchor found: 1.6 Architecture Overview → #16-architecture-overview"}, {"text": "1.7 Documentation Priority Order", "url": "#17-documentation-priority-order", "type": "anchor", "status": "Anchor found: 1.7 Documentation Priority Order → #17-documentation-priority-order"}, {"text": "1.8 Critical Requirements", "url": "#18-critical-requirements", "type": "anchor", "status": "An<PERSON> found: 1.8 Critical Requirements → #18-critical-requirements"}, {"text": "1.9 Implementation Roadmap", "url": "#19-implementation-roadmap", "type": "anchor", "status": "Anchor found: 1.9 Implementation Roadmap → #19-implementation-roadmap"}, {"text": "Chinook Index Guide", "url": "000-chinook-index.md", "type": "internal", "status": "File exists: 000-chinook-index.md"}, {"text": "Chinook Models Guide", "url": "010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Chinook Migrations Guide", "url": "020-chinook-migrations-guide.md", "type": "internal", "status": "File exists: 020-chinook-migrations-guide.md"}, {"text": "Chinook Factories Guide", "url": "030-chinook-factories-guide.md", "type": "internal", "status": "File exists: 030-chinook-factories-guide.md"}, {"text": "Chinook Seeders Guide", "url": "040-chinook-seeders-guide.md", "type": "internal", "status": "File exists: 040-chinook-seeders-guide.md"}, {"text": "Chinook Advanced Features Guide", "url": "050-chinook-advanced-features-guide.md", "type": "internal", "status": "File exists: 050-chinook-advanced-features-guide.md"}, {"text": "Chinook Media Library Guide", "url": "060-chinook-media-library-guide.md", "type": "internal", "status": "File exists: 060-chinook-media-library-guide.md"}, {"text": "Chinook Hierarchy Comparison Guide", "url": "070-chinook-hierarchy-comparison-guide.md", "type": "internal", "status": "File exists: 070-chinook-hierarchy-comparison-guide.md"}, {"text": "Visual Documentation Guide", "url": "080-visual-documentation-guide.md", "type": "internal", "status": "File exists: 080-visual-documentation-guide.md"}, {"text": "Relationship Mapping Guide", "url": "090-relationship-mapping.md", "type": "internal", "status": "File exists: 090-relationship-mapping.md"}, {"text": "Resource Testing Guide", "url": "100-resource-testing.md", "type": "internal", "status": "File exists: 100-resource-testing.md"}, {"text": "Authentication Flow Guide", "url": "110-authentication-flow.md", "type": "internal", "status": "File exists: 110-authentication-flow.md"}, {"text": "Laravel Query Builder Guide", "url": "120-laravel-query-builder-guide.md", "type": "internal", "status": "File exists: 120-laravel-query-builder-guide.md"}, {"text": "Comprehensive Data Access Guide", "url": "130-comprehensive-data-access-guide.md", "type": "internal", "status": "File exists: 130-comprehensive-data-access-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "packages/110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Single Taxonomy Architecture", "url": "070-chinook-hierarchy-comparison-guide.md", "type": "internal", "status": "File exists: 070-chinook-hierarchy-comparison-guide.md"}, {"text": "Filament Panel Overview", "url": "filament/000-filament-index.md", "type": "internal", "status": "File exists: filament/000-filament-index.md"}, {"text": "Model Standards", "url": "filament/models/000-models-index.md", "type": "internal", "status": "File exists: filament/models/000-models-index.md"}, {"text": "Resource Documentation", "url": "filament/resources/000-resources-index.md", "type": "internal", "status": "File exists: filament/resources/000-resources-index.md"}, {"text": "Advanced Features", "url": "filament/features/000-features-index.md", "type": "internal", "status": "File exists: filament/features/000-features-index.md"}, {"text": "Deployment Guide", "url": "filament/deployment/000-deployment-index.md", "type": "internal", "status": "File exists: filament/deployment/000-deployment-index.md"}, {"text": "Visual Documentation", "url": "filament/diagrams/000-diagrams-index.md", "type": "internal", "status": "File exists: filament/diagrams/000-diagrams-index.md"}, {"text": "Frontend Index", "url": "frontend/000-frontend-index.md", "type": "internal", "status": "File exists: frontend/000-frontend-index.md"}, {"text": "Frontend Architecture Overview", "url": "frontend/100-frontend-architecture-overview.md", "type": "internal", "status": "File exists: frontend/100-frontend-architecture-overview.md"}, {"text": "Volt Functional Patterns", "url": "frontend/110-volt-functional-patterns-guide.md", "type": "internal", "status": "File exists: frontend/110-volt-functional-patterns-guide.md"}, {"text": "Flux Component Integration", "url": "frontend/120-flux-component-integration-guide.md", "type": "internal", "status": "File exists: frontend/120-flux-component-integration-guide.md"}, {"text": "SPA Navigation Guide", "url": "frontend/130-spa-navigation-guide.md", "type": "internal", "status": "File exists: frontend/130-spa-navigation-guide.md"}, {"text": "Accessibility WCAG Guide", "url": "frontend/140-accessibility-wcag-guide.md", "type": "internal", "status": "File exists: frontend/140-accessibility-wcag-guide.md"}, {"text": "Performance Optimization", "url": "frontend/150-performance-optimization-guide.md", "type": "internal", "status": "File exists: frontend/150-performance-optimization-guide.md"}, {"text": "Testing Approaches", "url": "frontend/160-testing-approaches-guide.md", "type": "internal", "status": "File exists: frontend/160-testing-approaches-guide.md"}, {"text": "Performance Monitoring", "url": "frontend/170-performance-monitoring-guide.md", "type": "internal", "status": "File exists: frontend/170-performance-monitoring-guide.md"}, {"text": "API Testing Guide", "url": "frontend/180-api-testing-guide.md", "type": "internal", "status": "File exists: frontend/180-api-testing-guide.md"}, {"text": "CI/CD Integration", "url": "frontend/190-cicd-integration-guide.md", "type": "internal", "status": "File exists: frontend/190-cicd-integration-guide.md"}, {"text": "Essential Packages", "url": "packages/000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}, {"text": "Testing Guide", "url": "testing/000-testing-index.md", "type": "internal", "status": "File exists: testing/000-testing-index.md"}, {"text": "DBML Schema", "url": "chinook-schema.dbml", "type": "internal", "status": "File exists: chinook-schema.dbml"}, {"text": "Chinook Index Guide", "url": "000-chinook-index.md", "type": "internal", "status": "File exists: 000-chinook-index.md"}, {"text": "Data Access Guide", "url": "130-comprehensive-data-access-guide.md", "type": "internal", "status": "File exists: 130-comprehensive-data-access-guide.md"}]}, {"file": "filament/000-filament-index.md", "total_links": 45, "internal_links": 15, "anchor_links": 30, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2. Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2. <PERSON><PERSON> → #12-overview"}, {"text": "2. Architecture", "url": "#2-architecture", "type": "anchor", "status": "Anchor found: 2. Architecture → #2-architecture"}, {"text": "3. Documentation Structure", "url": "#3-documentation-structure", "type": "anchor", "status": "Anchor found: 3. Documentation Structure → #3-documentation-structure"}, {"text": "3.1. Setup Documentation", "url": "#31-setup-documentation", "type": "anchor", "status": "Anchor found: 3.1. Setup Documentation → #31-setup-documentation"}, {"text": "3.2. Resources Documentation", "url": "#32-resources-documentation", "type": "anchor", "status": "Anchor found: 3.2. Resources Documentation → #32-resources-documentation"}, {"text": "3.3. Features Documentation", "url": "#33-features-documentation", "type": "anchor", "status": "Anchor found: 3.3. Features Documentation → #33-features-documentation"}, {"text": "3.4. Models Documentation", "url": "#34-models-documentation", "type": "anchor", "status": "Anchor found: 3.4. Models Documentation → #34-models-documentation"}, {"text": "3.5. Testing Documentation", "url": "#35-testing-documentation", "type": "anchor", "status": "Anchor found: 3.5. Testing Documentation → #35-testing-documentation"}, {"text": "3.6. Deployment Documentation", "url": "#36-deployment-documentation", "type": "anchor", "status": "Anchor found: 3.6. Deployment Documentation → #36-deployment-documentation"}, {"text": "3.7. Diagrams Documentation", "url": "#37-diagrams-documentation", "type": "anchor", "status": "Anchor found: 3.7. Diagrams Documentation → #37-diagrams-documentation"}, {"text": "4. Quick Start Guide", "url": "#4-quick-start-guide", "type": "anchor", "status": "Anchor found: 4. Quick Start Guide → #4-quick-start-guide"}, {"text": "4.1. Prerequisites", "url": "#41-prerequisites", "type": "anchor", "status": "Anchor found: 4.1. Prerequisites → #41-prerequisites"}, {"text": "4.2. Installation Steps", "url": "#42-installation-steps", "type": "anchor", "status": "Anchor found: 4.2. Installation Steps → #42-installation-steps"}, {"text": "4.3. Configuration", "url": "#43-configuration", "type": "anchor", "status": "Anchor found: 4.3. Configuration → #43-configuration"}, {"text": "5. Panel Features", "url": "#5-panel-features", "type": "anchor", "status": "Anchor found: 5. Panel Features → #5-panel-features"}, {"text": "5.1. Authentication & Authorization", "url": "#51-authentication--authorization", "type": "anchor", "status": "Anchor found: 5.1. Authentication & Authorization → #51-authentication--authorization"}, {"text": "5.2. Resource Management", "url": "#52-resource-management", "type": "anchor", "status": "Anchor found: 5.2. Resource Management → #52-resource-management"}, {"text": "5.3. Advanced Features", "url": "#53-advanced-features", "type": "anchor", "status": "Anchor found: 5.3. Advanced Features → #53-advanced-features"}, {"text": "6. RBAC Integration", "url": "#6-rbac-integration", "type": "anchor", "status": "Anchor found: 6. RBAC Integration → #6-rbac-integration"}, {"text": "6.1. Hierarchical Roles", "url": "#61-hierarchical-roles", "type": "anchor", "status": "Anchor found: 6.1. Hierarchical Roles → #61-hierarchical-roles"}, {"text": "6.2. <PERSON><PERSON> Permissions", "url": "#62-granular-permissions", "type": "anchor", "status": "Anchor found: 6.2. Gran<PERSON> Permissions → #62-granular-permissions"}, {"text": "6.3. Access Control", "url": "#63-access-control", "type": "anchor", "status": "Anchor found: 6.3. Access Control → #63-access-control"}, {"text": "7. Performance & Security", "url": "#7-performance--security", "type": "anchor", "status": "Anchor found: 7. Performance & Security → #7-performance--security"}, {"text": "7.1. Optimization Strategies", "url": "#71-optimization-strategies", "type": "anchor", "status": "<PERSON><PERSON> found: 7.1. Optimization Strategies → #71-optimization-strategies"}, {"text": "7.2. Security Measures", "url": "#72-security-measures", "type": "anchor", "status": "An<PERSON> found: 7.2. Security Measures → #72-security-measures"}, {"text": "8. Standards Compliance", "url": "#8-standards-compliance", "type": "anchor", "status": "Anchor found: 8. Standards Compliance → #8-standards-compliance"}, {"text": "8.1. <PERSON><PERSON> 12 Modern Patterns", "url": "#81-laravel-12-modern-patterns", "type": "anchor", "status": "Anchor found: 8.1. <PERSON><PERSON> 12 Modern Patterns → #81-laravel-12-modern-patterns"}, {"text": "8.2. WCAG 2.1 AA Accessibility", "url": "#82-wcag-21-aa-accessibility", "type": "anchor", "status": "Anchor found: 8.2. WCAG 2.1 AA Accessibility → #82-wcag-21-aa-accessibility"}, {"text": "8.3. Documentation Standards", "url": "#83-documentation-standards", "type": "anchor", "status": "Anchor found: 8.3. Documentation Standards → #83-documentation-standards"}, {"text": "Deployment Index", "url": "deployment/000-deployment-index.md", "type": "internal", "status": "File exists: filament/deployment/000-deployment-index.md"}, {"text": "Deployment Guide", "url": "deployment/010-deployment-guide.md", "type": "internal", "status": "File exists: filament/deployment/010-deployment-guide.md"}, {"text": "Resources Index", "url": "resources/000-resources-index.md", "type": "internal", "status": "File exists: filament/resources/000-resources-index.md"}, {"text": "ChinookTracks Resource", "url": "resources/030-tracks-resource.md", "type": "internal", "status": "File exists: filament/resources/030-tracks-resource.md"}, {"text": "Taxonomy Resource", "url": "resources/040-taxonomy-resource.md", "type": "internal", "status": "File exists: filament/resources/040-taxonomy-resource.md"}, {"text": "Features Index", "url": "features/000-features-index.md", "type": "internal", "status": "File exists: filament/features/000-features-index.md"}, {"text": "Models Index", "url": "models/000-models-index.md", "type": "internal", "status": "File exists: filament/models/000-models-index.md"}, {"text": "Taxonomy Integration", "url": "models/090-taxonomy-integration.md", "type": "internal", "status": "File exists: filament/models/090-taxonomy-integration.md"}, {"text": "Testing Index", "url": "../testing/000-testing-index.md", "type": "internal", "status": "File exists: testing/000-testing-index.md"}, {"text": "Deployment Index", "url": "deployment/000-deployment-index.md", "type": "internal", "status": "File exists: filament/deployment/000-deployment-index.md"}, {"text": "Deployment Guide", "url": "deployment/010-deployment-guide.md", "type": "internal", "status": "File exists: filament/deployment/010-deployment-guide.md"}, {"text": "Diagrams Index", "url": "diagrams/000-diagrams-index.md", "type": "internal", "status": "File exists: filament/diagrams/000-diagrams-index.md"}, {"text": "Entity Relationship Diagrams", "url": "diagrams/010-entity-relationship-diagrams.md", "type": "internal", "status": "File exists: filament/diagrams/010-entity-relationship-diagrams.md"}, {"text": "000-chinook-index.md", "url": "../000-chinook-index.md", "type": "internal", "status": "File exists: 000-chinook-index.md"}, {"text": "Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1. Table of Contents → #11-table-of-contents"}, {"text": "Resources Index", "url": "resources/000-resources-index.md", "type": "internal", "status": "File exists: filament/resources/000-resources-index.md"}]}, {"file": "filament/deployment/000-deployment-index.md", "total_links": 14, "internal_links": 4, "anchor_links": 10, "external_links": 0, "broken_links": [{"text": "Production Environment", "url": "010-production-environment.md", "type": "internal", "status": "File not found: filament/deployment/010-production-environment.md"}], "working_links": [{"text": "1.1 Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1 Table of Contents → #11-table-of-contents"}, {"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Documentation Structure", "url": "#13-documentation-structure", "type": "anchor", "status": "Anchor found: 1.3 Documentation Structure → #13-documentation-structure"}, {"text": "1.4 Deployment Architecture", "url": "#14-deployment-architecture", "type": "anchor", "status": "Anchor found: 1.4 Deployment Architecture → #14-deployment-architecture"}, {"text": "1.5 Taxonomy-Specific Deployment Considerations", "url": "#15-taxonomy-specific-deployment-considerations", "type": "anchor", "status": "<PERSON><PERSON> found: 1.5 Taxonomy-Specific Deployment Considerations → #15-taxonomy-specific-deployment-considerations"}, {"text": "1.6 Performance Optimization", "url": "#16-performance-optimization", "type": "anchor", "status": "Anchor found: 1.6 Performance Optimization → #16-performance-optimization"}, {"text": "1.7 Security Hardening", "url": "#17-security-hardening", "type": "anchor", "status": "Anchor found: 1.7 Security Hardening → #17-security-hardening"}, {"text": "1.8 Monitoring & Maintenance", "url": "#18-monitoring--maintenance", "type": "anchor", "status": "Anchor found: 1.8 Monitoring & Maintenance → #18-monitoring--maintenance"}, {"text": "1.9 Deployment Automation", "url": "#19-deployment-automation", "type": "anchor", "status": "Anchor found: 1.9 Deployment Automation → #19-deployment-automation"}, {"text": "1.10 Best Practices", "url": "#110-best-practices", "type": "anchor", "status": "Anchor found: 1.10 Best Practices → #110-best-practices"}, {"text": "Filament Index", "url": "../000-filament-index.md", "type": "internal", "status": "File exists: filament/000-filament-index.md"}, {"text": "Filament Documentation", "url": "../000-filament-index.md", "type": "internal", "status": "File exists: filament/000-filament-index.md"}, {"text": "Chinook Documentation", "url": "../../README.md", "type": "internal", "status": "File exists: README.md"}]}, {"file": "filament/deployment/010-deployment-guide.md", "total_links": 9, "internal_links": 3, "anchor_links": 6, "external_links": 0, "broken_links": [], "working_links": [{"text": "2.1 Table of Contents", "url": "#21-table-of-contents", "type": "anchor", "status": "Anchor found: 2.1 Table of Contents → #21-table-of-contents"}, {"text": "2.2 Overview", "url": "#22-overview", "type": "anchor", "status": "Anchor found: 2.2 Overview → #22-overview"}, {"text": "2.3 Production Environment Setup", "url": "#23-production-environment-setup", "type": "anchor", "status": "Anchor found: 2.3 Production Environment Setup → #23-production-environment-setup"}, {"text": "2.4 Docker Deployment", "url": "#24-docker-deployment", "type": "anchor", "status": "Anchor found: 2.4 Docker Deployment → #24-docker-deployment"}, {"text": "2.5 Cloud Deployment Options", "url": "#25-cloud-deployment-options", "type": "anchor", "status": "Anchor found: 2.5 Cloud Deployment Options → #25-cloud-deployment-options"}, {"text": "2.12 Taxonomy-Specific Deployment", "url": "#212-taxonomy-specific-deployment", "type": "anchor", "status": "An<PERSON> found: 2.12 Taxonomy-Specific Deployment → #212-taxonomy-specific-deployment"}, {"text": "Deployment Index", "url": "000-deployment-index.md", "type": "internal", "status": "File exists: filament/deployment/000-deployment-index.md"}, {"text": "Deployment Documentation", "url": "000-deployment-index.md", "type": "internal", "status": "File exists: filament/deployment/000-deployment-index.md"}, {"text": "Chinook Documentation", "url": "../../README.md", "type": "internal", "status": "File exists: README.md"}]}, {"file": "filament/diagrams/000-diagrams-index.md", "total_links": 15, "internal_links": 5, "anchor_links": 10, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.1 Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1 Table of Contents → #11-table-of-contents"}, {"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Documentation Structure", "url": "#13-documentation-structure", "type": "anchor", "status": "Anchor found: 1.3 Documentation Structure → #13-documentation-structure"}, {"text": "1.4 WCAG 2.1 AA Compliance", "url": "#14-wcag-21-aa-compliance", "type": "anchor", "status": "Anchor found: 1.4 WCAG 2.1 AA Compliance → #14-wcag-21-aa-compliance"}, {"text": "1.5 Taxonomy Integration Architecture", "url": "#15-taxonomy-integration-architecture", "type": "anchor", "status": "<PERSON><PERSON> found: 1.5 Taxonomy Integration Architecture → #15-taxonomy-integration-architecture"}, {"text": "1.6 Entity Relationship Diagram", "url": "#16-entity-relationship-diagram", "type": "anchor", "status": "An<PERSON> found: 1.6 Entity Relationship Diagram → #16-entity-relationship-diagram"}, {"text": "1.7 System Architecture Overview", "url": "#17-system-architecture-overview", "type": "anchor", "status": "Anchor found: 1.7 System Architecture Overview → #17-system-architecture-overview"}, {"text": "1.8 Performance Architecture", "url": "#18-performance-architecture", "type": "anchor", "status": "Anchor found: 1.8 Performance Architecture → #18-performance-architecture"}, {"text": "1.9 Security Architecture", "url": "#19-security-architecture", "type": "anchor", "status": "Anchor found: 1.9 Security Architecture → #19-security-architecture"}, {"text": "1.10 Best Practices", "url": "#110-best-practices", "type": "anchor", "status": "Anchor found: 1.10 Best Practices → #110-best-practices"}, {"text": "Entity Relationship Diagrams", "url": "010-entity-relationship-diagrams.md", "type": "internal", "status": "File exists: filament/diagrams/010-entity-relationship-diagrams.md"}, {"text": "Filament Index", "url": "../000-filament-index.md", "type": "internal", "status": "File exists: filament/000-filament-index.md"}, {"text": "Entity Relationship Diagrams", "url": "010-entity-relationship-diagrams.md", "type": "internal", "status": "File exists: filament/diagrams/010-entity-relationship-diagrams.md"}, {"text": "Filament Documentation", "url": "../000-filament-index.md", "type": "internal", "status": "File exists: filament/000-filament-index.md"}, {"text": "Chinook Documentation", "url": "../../README.md", "type": "internal", "status": "File exists: README.md"}]}, {"file": "filament/diagrams/010-entity-relationship-diagrams.md", "total_links": 8, "internal_links": 3, "anchor_links": 5, "external_links": 0, "broken_links": [], "working_links": [{"text": "2.1 Table of Contents", "url": "#21-table-of-contents", "type": "anchor", "status": "Anchor found: 2.1 Table of Contents → #21-table-of-contents"}, {"text": "2.2 Overview", "url": "#22-overview", "type": "anchor", "status": "Anchor found: 2.2 Overview → #22-overview"}, {"text": "2.3 Complete Database ERD", "url": "#23-complete-database-erd", "type": "anchor", "status": "Anchor found: 2.3 Complete Database ERD → #23-complete-database-erd"}, {"text": "2.4 Taxonomy System ERD", "url": "#24-taxonomy-system-erd", "type": "anchor", "status": "An<PERSON> found: 2.4 Taxonomy System ERD → #24-taxonomy-system-erd"}, {"text": "2.7 Performance Optimization", "url": "#27-performance-optimization", "type": "anchor", "status": "Anchor found: 2.7 Performance Optimization → #27-performance-optimization"}, {"text": "Diagrams Index", "url": "000-diagrams-index.md", "type": "internal", "status": "File exists: filament/diagrams/000-diagrams-index.md"}, {"text": "Diagrams Documentation", "url": "000-diagrams-index.md", "type": "internal", "status": "File exists: filament/diagrams/000-diagrams-index.md"}, {"text": "Chinook Documentation", "url": "../../README.md", "type": "internal", "status": "File exists: README.md"}]}, {"file": "filament/features/000-features-index.md", "total_links": 5, "internal_links": 5, "anchor_links": 0, "external_links": 0, "broken_links": [{"text": "Dashboard Configuration", "url": "010-dashboard-configuration.md", "type": "internal", "status": "File not found: filament/features/010-dashboard-configuration.md"}], "working_links": [{"text": "Media Library", "url": "../../060-chinook-media-library-guide.md", "type": "internal", "status": "File exists: 060-chinook-media-library-guide.md"}, {"text": "Activity Logging", "url": "../../packages/160-spatie-activitylog-guide.md", "type": "internal", "status": "File exists: packages/160-spatie-activitylog-guide.md"}, {"text": "Taxonomy Analytics", "url": "../../packages/110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Filament Documentation", "url": "../000-filament-index.md", "type": "internal", "status": "File exists: filament/000-filament-index.md"}]}, {"file": "filament/internationalization/000-internationalization-index.md", "total_links": 14, "internal_links": 4, "anchor_links": 10, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.1 Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1 Table of Contents → #11-table-of-contents"}, {"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Supported Languages", "url": "#13-supported-languages", "type": "anchor", "status": "Anchor found: 1.3 Supported Languages → #13-supported-languages"}, {"text": "1.4 Integration Features", "url": "#14-integration-features", "type": "anchor", "status": "Anchor found: 1.4 Integration Features → #14-integration-features"}, {"text": "1.5 Core Implementation", "url": "#15-core-implementation", "type": "anchor", "status": "Anchor found: 1.5 Core Implementation → #15-core-implementation"}, {"text": "1.6 Taxonomy Internationalization", "url": "#16-taxonomy-internationalization", "type": "anchor", "status": "<PERSON><PERSON> found: 1.6 Taxonomy Internationalization → #16-taxonomy-internationalization"}, {"text": "1.7 Advanced Features", "url": "#17-advanced-features", "type": "anchor", "status": "Anchor found: 1.7 Advanced Features → #17-advanced-features"}, {"text": "1.8 Implementation Standards", "url": "#18-implementation-standards", "type": "anchor", "status": "Anchor found: 1.8 Implementation Standards → #18-implementation-standards"}, {"text": "1.9 Performance Optimization", "url": "#19-performance-optimization", "type": "anchor", "status": "Anchor found: 1.9 Performance Optimization → #19-performance-optimization"}, {"text": "1.10 Best Practices", "url": "#110-best-practices", "type": "anchor", "status": "Anchor found: 1.10 Best Practices → #110-best-practices"}, {"text": "Filament Index", "url": "../000-filament-index.md", "type": "internal", "status": "File exists: filament/000-filament-index.md"}, {"text": "Translatable Models Setup", "url": "010-translatable-models-setup.md", "type": "internal", "status": "File exists: filament/internationalization/010-translatable-models-setup.md"}, {"text": "Filament Documentation", "url": "../000-filament-index.md", "type": "internal", "status": "File exists: filament/000-filament-index.md"}, {"text": "Chinook Documentation", "url": "../../README.md", "type": "internal", "status": "File exists: README.md"}]}, {"file": "filament/internationalization/010-translatable-models-setup.md", "total_links": 12, "internal_links": 4, "anchor_links": 8, "external_links": 0, "broken_links": [], "working_links": [{"text": "2.1 Table of Contents", "url": "#21-table-of-contents", "type": "anchor", "status": "Anchor found: 2.1 Table of Contents → #21-table-of-contents"}, {"text": "2.2 Overview", "url": "#22-overview", "type": "anchor", "status": "Anchor found: 2.2 Overview → #22-overview"}, {"text": "2.3 Supported Models", "url": "#23-supported-models", "type": "anchor", "status": "Anchor found: 2.3 Supported Models → #23-supported-models"}, {"text": "2.4 Translation Strategy", "url": "#24-translation-strategy", "type": "anchor", "status": "An<PERSON> found: 2.4 Translation Strategy → #24-translation-strategy"}, {"text": "2.5 Model Configuration", "url": "#25-model-configuration", "type": "anchor", "status": "Anchor found: 2.5 Model Configuration → #25-model-configuration"}, {"text": "2.6 Taxonomy Translation Setup", "url": "#26-taxonomy-translation-setup", "type": "anchor", "status": "Anchor found: 2.6 Taxonomy Translation Setup → #26-taxonomy-translation-setup"}, {"text": "2.7 Database Migrations", "url": "#27-database-migrations", "type": "anchor", "status": "Anchor found: 2.7 Database Migrations → #27-database-migrations"}, {"text": "2.8 Usage Examples", "url": "#28-usage-examples", "type": "anchor", "status": "Anchor found: 2.8 Usage Examples → #28-usage-examples"}, {"text": "Internationalization Index", "url": "000-internationalization-index.md", "type": "internal", "status": "File exists: filament/internationalization/000-internationalization-index.md"}, {"text": "Internationalization Index", "url": "000-internationalization-index.md", "type": "internal", "status": "File exists: filament/internationalization/000-internationalization-index.md"}, {"text": "Internationalization Documentation", "url": "000-internationalization-index.md", "type": "internal", "status": "File exists: filament/internationalization/000-internationalization-index.md"}, {"text": "Chinook Documentation", "url": "../../README.md", "type": "internal", "status": "File exists: README.md"}]}, {"file": "filament/models/000-models-index.md", "total_links": 6, "internal_links": 4, "anchor_links": 0, "external_links": 2, "broken_links": [], "working_links": [{"text": "Taxonomy Integration", "url": "090-taxonomy-integration.md", "type": "internal", "status": "File exists: filament/models/090-taxonomy-integration.md"}, {"text": "Taxonomy Resource", "url": "../resources/040-taxonomy-resource.md", "type": "internal", "status": "File exists: filament/resources/040-taxonomy-resource.md"}, {"text": "aliziodev/laravel-taxonomy Documentation", "url": "https://github.com/aliziodev/laravel-taxonomy", "type": "external", "status": "External link (not validated)"}, {"text": "Laravel 12 Eloquent", "url": "https://laravel.com/docs/12.x/eloquent", "type": "external", "status": "External link (not validated)"}, {"text": "Filament Documentation", "url": "../000-filament-index.md", "type": "internal", "status": "File exists: filament/000-filament-index.md"}, {"text": "Taxonomy Integration", "url": "090-taxonomy-integration.md", "type": "internal", "status": "File exists: filament/models/090-taxonomy-integration.md"}]}, {"file": "filament/models/090-taxonomy-integration.md", "total_links": 10, "internal_links": 1, "anchor_links": 9, "external_links": 0, "broken_links": [], "working_links": [{"text": "9.2. Overview", "url": "#92-overview", "type": "anchor", "status": "Anchor found: 9.2. <PERSON><PERSON> → #92-overview"}, {"text": "10. Single Taxonomy System", "url": "#10-single-taxonomy-system", "type": "anchor", "status": "An<PERSON> found: 10. Single Taxonomy System → #10-single-taxonomy-system"}, {"text": "10.1. Package Integration", "url": "#101-package-integration", "type": "anchor", "status": "Anchor found: 10.1. Package Integration → #101-package-integration"}, {"text": "11. Taxonomy Architecture", "url": "#11-taxonomy-architecture", "type": "anchor", "status": "Anchor found: 11. Taxonomy Architecture → #11-taxonomy-architecture"}, {"text": "11.1. Hierarchical Structure", "url": "#111-hierarchical-structure", "type": "anchor", "status": "Anchor found: 11.1. Hierarchical Structure → #111-hierarchical-structure"}, {"text": "12. Genre Preservation Strategy", "url": "#12-genre-preservation-strategy", "type": "anchor", "status": "Anchor found: 12. Genre Preservation Strategy → #12-genre-preservation-strategy"}, {"text": "12.1. Compatibility Layer", "url": "#121-compatibility-layer", "type": "anchor", "status": "Anchor found: 12.1. Compatibility Layer → #121-compatibility-layer"}, {"text": "13. <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "url": "#13-query-scopes-and-filters", "type": "anchor", "status": "Anchor found: 13. Query Scopes and Filters → #13-query-scopes-and-filters"}, {"text": "13.1. Basic Filtering", "url": "#131-basic-filtering", "type": "anchor", "status": "Anchor found: 13.1. Basic Filtering → #131-basic-filtering"}, {"text": "Models Index", "url": "000-models-index.md", "type": "internal", "status": "File exists: filament/models/000-models-index.md"}]}, {"file": "filament/resources/000-resources-index.md", "total_links": 4, "internal_links": 4, "anchor_links": 0, "external_links": 0, "broken_links": [], "working_links": [{"text": "Tracks Resource", "url": "030-tracks-resource.md", "type": "internal", "status": "File exists: filament/resources/030-tracks-resource.md"}, {"text": "Taxonomy Resource", "url": "040-taxonomy-resource.md", "type": "internal", "status": "File exists: filament/resources/040-taxonomy-resource.md"}, {"text": "Filament Documentation", "url": "../000-filament-index.md", "type": "internal", "status": "File exists: filament/000-filament-index.md"}, {"text": "Tracks Resource", "url": "030-tracks-resource.md", "type": "internal", "status": "File exists: filament/resources/030-tracks-resource.md"}]}, {"file": "filament/resources/030-tracks-resource.md", "total_links": 11, "internal_links": 2, "anchor_links": 9, "external_links": 0, "broken_links": [], "working_links": [{"text": "3.2. Overview", "url": "#32-overview", "type": "anchor", "status": "Anchor found: 3.2. <PERSON><PERSON> → #32-overview"}, {"text": "4. Resource Implementation", "url": "#4-resource-implementation", "type": "anchor", "status": "Anchor found: 4. Resource Implementation → #4-resource-implementation"}, {"text": "4.1. Basic Resource Structure", "url": "#41-basic-resource-structure", "type": "anchor", "status": "Anchor found: 4.1. Basic Resource Structure → #41-basic-resource-structure"}, {"text": "5. Taxonomy Integration", "url": "#5-taxonomy-integration", "type": "anchor", "status": "<PERSON><PERSON> found: 5. Taxonomy Integration → #5-taxonomy-integration"}, {"text": "5.1. Genre Management", "url": "#51-genre-management", "type": "anchor", "status": "Anchor found: 5.1. Genre Management → #51-genre-management"}, {"text": "6. Complex Relationships", "url": "#6-complex-relationships", "type": "anchor", "status": "<PERSON><PERSON> found: 6. Complex Relationships → #6-complex-relationships"}, {"text": "6.1. Album Relationship", "url": "#61-album-relationship", "type": "anchor", "status": "An<PERSON> found: 6.1. Album Relationship → #61-album-relationship"}, {"text": "7. Advanced Features", "url": "#7-advanced-features", "type": "anchor", "status": "Anchor found: 7. Advanced Features → #7-advanced-features"}, {"text": "7.1. Audio File Management", "url": "#71-audio-file-management", "type": "anchor", "status": "Anchor found: 7.1. Audio File Management → #71-audio-file-management"}, {"text": "Resources Index", "url": "000-resources-index.md", "type": "internal", "status": "File exists: filament/resources/000-resources-index.md"}, {"text": "Taxonomy Resource", "url": "040-taxonomy-resource.md", "type": "internal", "status": "File exists: filament/resources/040-taxonomy-resource.md"}]}, {"file": "filament/resources/040-taxonomy-resource.md", "total_links": 11, "internal_links": 2, "anchor_links": 9, "external_links": 0, "broken_links": [], "working_links": [{"text": "4.2. Overview", "url": "#42-overview", "type": "anchor", "status": "Anchor found: 4.2. <PERSON><PERSON> → #42-overview"}, {"text": "5. Resource Implementation", "url": "#5-resource-implementation", "type": "anchor", "status": "Anchor found: 5. Resource Implementation → #5-resource-implementation"}, {"text": "5.1. Basic Resource Structure", "url": "#51-basic-resource-structure", "type": "anchor", "status": "Anchor found: 5.1. Basic Resource Structure → #51-basic-resource-structure"}, {"text": "6. Hierarchical Data Management", "url": "#6-hierarchical-data-management", "type": "anchor", "status": "Anchor found: 6. Hierarchical Data Management → #6-hierarchical-data-management"}, {"text": "6.1. Taxonomy Architecture", "url": "#61-taxonomy-architecture", "type": "anchor", "status": "An<PERSON> found: 6.1. Taxonomy Architecture → #61-taxonomy-architecture"}, {"text": "7. Polymorphic Relationships", "url": "#7-polymorphic-relationships", "type": "anchor", "status": "An<PERSON> found: 7. Polymorphic Relationships → #7-polymorphic-relationships"}, {"text": "7.1. HasTaxonomies Implementation", "url": "#71-hastaxonomies-implementation", "type": "anchor", "status": "Anchor found: 7.1. HasTaxonomies Implementation → #71-hastaxonomies-implementation"}, {"text": "8. Advanced Features", "url": "#8-advanced-features", "type": "anchor", "status": "Anchor found: 8. Advanced Features → #8-advanced-features"}, {"text": "8.1. Taxonomy Tree Visualization", "url": "#81-taxonomy-tree-visualization", "type": "anchor", "status": "An<PERSON> found: 8.1. Taxonomy Tree Visualization → #81-taxonomy-tree-visualization"}, {"text": "ChinookTracks Resource", "url": "030-tracks-resource.md", "type": "internal", "status": "File exists: filament/resources/030-tracks-resource.md"}, {"text": "Resources Index", "url": "000-resources-index.md", "type": "internal", "status": "File exists: filament/resources/000-resources-index.md"}]}, {"file": "frontend/000-frontend-index.md", "total_links": 19, "internal_links": 14, "anchor_links": 5, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2. Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2. <PERSON><PERSON> → #12-overview"}, {"text": "1.3. Documentation Structure", "url": "#13-documentation-structure", "type": "anchor", "status": "Anchor found: 1.3. Documentation Structure → #13-documentation-structure"}, {"text": "1.3.1. Core Architecture", "url": "#131-core-architecture", "type": "anchor", "status": "Anchor found: 1.3.1. Core Architecture → #131-core-architecture"}, {"text": "1.4. Technology Stack", "url": "#14-technology-stack", "type": "anchor", "status": "Anchor found: 1.4. <PERSON>ack → #14-technology-stack"}, {"text": "1.5. <PERSON> Patterns", "url": "#15-development-patterns", "type": "anchor", "status": "Anchor found: 1.5. <PERSON> Patterns → #15-development-patterns"}, {"text": "Frontend Architecture Overview", "url": "100-frontend-architecture-overview.md", "type": "internal", "status": "File exists: frontend/100-frontend-architecture-overview.md"}, {"text": "Volt Functional Patterns Guide", "url": "110-volt-functional-patterns-guide.md", "type": "internal", "status": "File exists: frontend/110-volt-functional-patterns-guide.md"}, {"text": "Flux Component Integration Guide", "url": "120-flux-component-integration-guide.md", "type": "internal", "status": "File exists: frontend/120-flux-component-integration-guide.md"}, {"text": "SPA Navigation Guide", "url": "130-spa-navigation-guide.md", "type": "internal", "status": "File exists: frontend/130-spa-navigation-guide.md"}, {"text": "Accessibility WCAG Guide", "url": "140-accessibility-wcag-guide.md", "type": "internal", "status": "File exists: frontend/140-accessibility-wcag-guide.md"}, {"text": "Livewire Volt Integration Guide", "url": "160-livewire-volt-integration-guide.md", "type": "internal", "status": "File exists: frontend/160-livewire-volt-integration-guide.md"}, {"text": "Performance Optimization Guide", "url": "150-performance-optimization-guide.md", "type": "internal", "status": "File exists: frontend/150-performance-optimization-guide.md"}, {"text": "Performance Monitoring Guide", "url": "170-performance-monitoring-guide.md", "type": "internal", "status": "File exists: frontend/170-performance-monitoring-guide.md"}, {"text": "Testing Approaches Guide", "url": "160-testing-approaches-guide.md", "type": "internal", "status": "File exists: frontend/160-testing-approaches-guide.md"}, {"text": "API Testing Guide", "url": "180-api-testing-guide.md", "type": "internal", "status": "File exists: frontend/180-api-testing-guide.md"}, {"text": "CI/CD Integration Guide", "url": "190-cicd-integration-guide.md", "type": "internal", "status": "File exists: frontend/190-cicd-integration-guide.md"}, {"text": "Media Library Enhancement Guide", "url": "200-media-library-enhancement-guide.md", "type": "internal", "status": "File exists: frontend/200-media-library-enhancement-guide.md"}, {"text": "Documentation Root", "url": "../000-chinook-index.md", "type": "internal", "status": "File exists: 000-chinook-index.md"}, {"text": "Frontend Architecture Overview", "url": "100-frontend-architecture-overview.md", "type": "internal", "status": "File exists: frontend/100-frontend-architecture-overview.md"}]}, {"file": "frontend/100-frontend-architecture-overview.md", "total_links": 13, "internal_links": 1, "anchor_links": 12, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Architecture Principles", "url": "#2-architecture-principles", "type": "anchor", "status": "An<PERSON> found: 2. Architecture Principles → #2-architecture-principles"}, {"text": "3. Technology Stack", "url": "#3-technology-stack", "type": "anchor", "status": "Anchor found: 3. <PERSON>ack → #3-technology-stack"}, {"text": "4. Component Hierarchy", "url": "#4-component-hierarchy", "type": "anchor", "status": "Anchor found: 4. Component Hierarchy → #4-component-hierarchy"}, {"text": "5. SPA Navigation Patterns", "url": "#5-spa-navigation-patterns", "type": "anchor", "status": "Anchor found: 5. SPA Navigation Patterns → #5-spa-navigation-patterns"}, {"text": "6. Integration Architecture", "url": "#6-integration-architecture", "type": "anchor", "status": "An<PERSON> found: 6. Integration Architecture → #6-integration-architecture"}, {"text": "7. Data Flow Patterns", "url": "#7-data-flow-patterns", "type": "anchor", "status": "Anchor found: 7. Data Flow Patterns → #7-data-flow-patterns"}, {"text": "8. Performance Considerations", "url": "#8-performance-considerations", "type": "anchor", "status": "Anchor found: 8. Performance Considerations → #8-performance-considerations"}, {"text": "9. Accessibility Foundation", "url": "#9-accessibility-foundation", "type": "anchor", "status": "An<PERSON> found: 9. Accessibility Foundation → #9-accessibility-foundation"}, {"text": "10. Development Workflow", "url": "#10-development-workflow", "type": "anchor", "status": "Anchor found: 10. Development Workflow → #10-development-workflow"}, {"text": "11. Best Practices", "url": "#11-best-practices", "type": "anchor", "status": "Anchor found: 11. Best Practices → #11-best-practices"}, {"text": "12. <PERSON>", "url": "#12-navigation", "type": "anchor", "status": "Anchor found: 12. <PERSON> → #12-navigation"}, {"text": "Volt Functional Component Patterns Guide", "url": "110-volt-functional-patterns-guide.md", "type": "internal", "status": "File exists: frontend/110-volt-functional-patterns-guide.md"}]}, {"file": "frontend/110-volt-functional-patterns-guide.md", "total_links": 16, "internal_links": 2, "anchor_links": 14, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Component Structure", "url": "#2-component-structure", "type": "anchor", "status": "Anchor found: 2. Component Structure → #2-component-structure"}, {"text": "3. State Management Patterns", "url": "#3-state-management-patterns", "type": "anchor", "status": "Anchor found: 3. State Management Patterns → #3-state-management-patterns"}, {"text": "4. <PERSON>", "url": "#4-action-patterns", "type": "anchor", "status": "Anchor found: 4. <PERSON> Patterns → #4-action-patterns"}, {"text": "5. Lifecycle Hooks", "url": "#5-lifecycle-hooks", "type": "anchor", "status": "Anchor found: 5. Life<PERSON> <PERSON><PERSON> → #5-lifecycle-hooks"}, {"text": "6. <PERSON>", "url": "#6-form-handling", "type": "anchor", "status": "Anchor found: 6. Form Handling → #6-form-handling"}, {"text": "7. Event Communication", "url": "#7-event-communication", "type": "anchor", "status": "Anchor found: 7. Event Communication → #7-event-communication"}, {"text": "8. Computed Properties", "url": "#8-computed-properties", "type": "anchor", "status": "Anchor found: 8. Computed Properties → #8-computed-properties"}, {"text": "9. URL State Synchronization", "url": "#9-url-state-synchronization", "type": "anchor", "status": "Anchor found: 9. URL State Synchronization → #9-url-state-synchronization"}, {"text": "10. Component Composition", "url": "#10-component-composition", "type": "anchor", "status": "Anchor found: 10. Component Composition → #10-component-composition"}, {"text": "11. Performance Optimization", "url": "#11-performance-optimization", "type": "anchor", "status": "Anchor found: 11. Performance Optimization → #11-performance-optimization"}, {"text": "12. <PERSON> Pat<PERSON>s", "url": "#12-testing-patterns", "type": "anchor", "status": "Anchor found: 12. <PERSON> Patterns → #12-testing-patterns"}, {"text": "13. Best Practices", "url": "#13-best-practices", "type": "anchor", "status": "Anchor found: 13. Best Practices → #13-best-practices"}, {"text": "14. <PERSON>", "url": "#14-navigation", "type": "anchor", "status": "Anchor found: 14. <PERSON> → #14-navigation"}, {"text": "Frontend Architecture Overview", "url": "100-frontend-architecture-overview.md", "type": "internal", "status": "File exists: frontend/100-frontend-architecture-overview.md"}, {"text": "Flux/Flux-Pro Component Integration Guide", "url": "120-flux-component-integration-guide.md", "type": "internal", "status": "File exists: frontend/120-flux-component-integration-guide.md"}]}, {"file": "frontend/120-flux-component-integration-guide.md", "total_links": 10, "internal_links": 2, "anchor_links": 8, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Flux Free Components", "url": "#2-flux-free-components", "type": "anchor", "status": "An<PERSON> found: 2. Flux Free Components → #2-flux-free-components"}, {"text": "3. Flux Pro Components", "url": "#3-flux-pro-components", "type": "anchor", "status": "An<PERSON> found: 3. Flux Pro Components → #3-flux-pro-components"}, {"text": "4. Data Display Components", "url": "#4-data-display-components", "type": "anchor", "status": "Anchor found: 4. Data Display Components → #4-data-display-components"}, {"text": "5. Navigation Components", "url": "#5-navigation-components", "type": "anchor", "status": "Anchor found: 5. Navigation Components → #5-navigation-components"}, {"text": "6. Interactive Components", "url": "#6-interactive-components", "type": "anchor", "status": "Anchor found: 6. Interactive Components → #6-interactive-components"}, {"text": "7. Best Practices", "url": "#7-best-practices", "type": "anchor", "status": "Anchor found: 7. Best Practices → #7-best-practices"}, {"text": "8. <PERSON>", "url": "#8-navigation", "type": "anchor", "status": "Anchor found: 8. <PERSON> → #8-navigation"}, {"text": "Volt Functional Component Patterns Guide", "url": "110-volt-functional-patterns-guide.md", "type": "internal", "status": "File exists: frontend/110-volt-functional-patterns-guide.md"}, {"text": "SPA Navigation Guide", "url": "130-spa-navigation-guide.md", "type": "internal", "status": "File exists: frontend/130-spa-navigation-guide.md"}]}, {"file": "frontend/130-spa-navigation-guide.md", "total_links": 16, "internal_links": 2, "anchor_links": 14, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Livewire Navigate Setup", "url": "#2-livewire-navigate-setup", "type": "anchor", "status": "Anchor found: 2. Livewire Navigate Setup → #2-livewire-navigate-setup"}, {"text": "3. Route Configuration", "url": "#3-route-configuration", "type": "anchor", "status": "Anchor found: 3. Route Configuration → #3-route-configuration"}, {"text": "4. <PERSON> Patterns", "url": "#4-navigation-patterns", "type": "anchor", "status": "Anchor found: 4. <PERSON> Patterns → #4-navigation-patterns"}, {"text": "5. State Management", "url": "#5-state-management", "type": "anchor", "status": "Anchor found: 5. State Management → #5-state-management"}, {"text": "6. Page Transitions", "url": "#6-page-transitions", "type": "anchor", "status": "Anchor found: 6. <PERSON> Transitions → #6-page-transitions"}, {"text": "7. URL Synchronization", "url": "#7-url-synchronization", "type": "anchor", "status": "Anchor found: 7. URL Synchronization → #7-url-synchronization"}, {"text": "8. History Management", "url": "#8-history-management", "type": "anchor", "status": "Anchor found: 8. History Management → #8-history-management"}, {"text": "9. <PERSON><PERSON><PERSON>", "url": "#9-error-handling", "type": "anchor", "status": "Anchor found: 9. <PERSON><PERSON><PERSON> → #9-error-handling"}, {"text": "10. Performance Optimization", "url": "#10-performance-optimization", "type": "anchor", "status": "Anchor found: 10. Performance Optimization → #10-performance-optimization"}, {"text": "11. SEO Considerations", "url": "#11-seo-considerations", "type": "anchor", "status": "Anchor found: 11. SEO Considerations → #11-seo-considerations"}, {"text": "12. Testing Navigation", "url": "#12-testing-navigation", "type": "anchor", "status": "Anchor found: 12. Testing Navigation → #12-testing-navigation"}, {"text": "13. Best Practices", "url": "#13-best-practices", "type": "anchor", "status": "Anchor found: 13. Best Practices → #13-best-practices"}, {"text": "14. <PERSON>", "url": "#14-navigation", "type": "anchor", "status": "Anchor found: 14. <PERSON> → #14-navigation"}, {"text": "Flux/Flux-Pro Component Integration Guide", "url": "120-flux-component-integration-guide.md", "type": "internal", "status": "File exists: frontend/120-flux-component-integration-guide.md"}, {"text": "Accessibility and WCAG Compliance Guide", "url": "140-accessibility-wcag-guide.md", "type": "internal", "status": "File exists: frontend/140-accessibility-wcag-guide.md"}]}, {"file": "frontend/140-accessibility-wcag-guide.md", "total_links": 12, "internal_links": 2, "anchor_links": 10, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. WCAG 2.1 AA Requirements", "url": "#2-wcag-21-aa-requirements", "type": "anchor", "status": "Anchor found: 2. WCAG 2.1 AA Requirements → #2-wcag-21-aa-requirements"}, {"text": "3. Semantic HTML Structure", "url": "#3-semantic-html-structure", "type": "anchor", "status": "Anchor found: 3. Semantic HTML Structure → #3-semantic-html-structure"}, {"text": "4. Keyboard Navigation", "url": "#4-keyboard-navigation", "type": "anchor", "status": "Anchor found: 4. Keyboard Navigation → #4-keyboard-navigation"}, {"text": "5. Screen Reader Support", "url": "#5-screen-reader-support", "type": "anchor", "status": "Anchor found: 5. Screen Reader Support → #5-screen-reader-support"}, {"text": "6. Color and Contrast", "url": "#6-color-and-contrast", "type": "anchor", "status": "Anchor found: 6. Color and Contrast → #6-color-and-contrast"}, {"text": "7. Form Accessibility", "url": "#7-form-accessibility", "type": "anchor", "status": "An<PERSON> found: 7. Form Accessibility → #7-form-accessibility"}, {"text": "8. Testing Strategies", "url": "#8-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 8. Testing Strategies → #8-testing-strategies"}, {"text": "9. Best Practices", "url": "#9-best-practices", "type": "anchor", "status": "Anchor found: 9. Best Practices → #9-best-practices"}, {"text": "10. Navigation", "url": "#10-navigation", "type": "anchor", "status": "Anchor found: 10. Navigation → #10-navigation"}, {"text": "SPA Navigation Implementation Guide", "url": "130-spa-navigation-guide.md", "type": "internal", "status": "File exists: frontend/130-spa-navigation-guide.md"}, {"text": "Performance Optimization Guide", "url": "150-performance-optimization-guide.md", "type": "internal", "status": "File exists: frontend/150-performance-optimization-guide.md"}]}, {"file": "frontend/150-performance-optimization-guide.md", "total_links": 12, "internal_links": 2, "anchor_links": 10, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Livewire Performance Patterns", "url": "#2-livewire-performance-patterns", "type": "anchor", "status": "Anchor found: 2. Livewire Performance Patterns → #2-livewire-performance-patterns"}, {"text": "3. <PERSON><PERSON>ading Strategies", "url": "#3-lazy-loading-strategies", "type": "anchor", "status": "Anchor found: 3. Lazy Loading Strategies → #3-lazy-loading-strategies"}, {"text": "4. Caching Mechanisms", "url": "#4-caching-mechanisms", "type": "anchor", "status": "Anchor found: 4. Caching Mechanisms → #4-caching-mechanisms"}, {"text": "5. Database Optimization", "url": "#5-database-optimization", "type": "anchor", "status": "Anchor found: 5. Database Optimization → #5-database-optimization"}, {"text": "6. Asset Optimization", "url": "#6-asset-optimization", "type": "anchor", "status": "<PERSON><PERSON> found: 6. Asset Optimization → #6-asset-optimization"}, {"text": "7. Memory Management", "url": "#7-memory-management", "type": "anchor", "status": "Anchor found: 7. Memory Management → #7-memory-management"}, {"text": "8. Network Optimization", "url": "#8-network-optimization", "type": "anchor", "status": "An<PERSON> found: 8. Network Optimization → #8-network-optimization"}, {"text": "9. Best Practices", "url": "#9-best-practices", "type": "anchor", "status": "Anchor found: 9. Best Practices → #9-best-practices"}, {"text": "10. Navigation", "url": "#10-navigation", "type": "anchor", "status": "Anchor found: 10. Navigation → #10-navigation"}, {"text": "Accessibility and WCAG Compliance Guide", "url": "140-accessibility-wcag-guide.md", "type": "internal", "status": "File exists: frontend/140-accessibility-wcag-guide.md"}, {"text": "Testing Approaches Guide", "url": "160-testing-approaches-guide.md", "type": "internal", "status": "File exists: frontend/160-testing-approaches-guide.md"}]}, {"file": "frontend/160-livewire-volt-integration-guide.md", "total_links": 12, "internal_links": 2, "anchor_links": 10, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Functional Component Architecture", "url": "#2-functional-component-architecture", "type": "anchor", "status": "Anchor found: 2. Functional Component Architecture → #2-functional-component-architecture"}, {"text": "3. Volt Integration Patterns", "url": "#3-volt-integration-patterns", "type": "anchor", "status": "Anchor found: 3. Volt Integration Patterns → #3-volt-integration-patterns"}, {"text": "4. Real-time Features", "url": "#4-real-time-features", "type": "anchor", "status": "Anchor found: 4. Real-time Features → #4-real-time-features"}, {"text": "5. State Management", "url": "#5-state-management", "type": "anchor", "status": "Anchor found: 5. State Management → #5-state-management"}, {"text": "6. Component Communication", "url": "#6-component-communication", "type": "anchor", "status": "Anchor found: 6. Component Communication → #6-component-communication"}, {"text": "7. Performance Optimization", "url": "#7-performance-optimization", "type": "anchor", "status": "Anchor found: 7. Performance Optimization → #7-performance-optimization"}, {"text": "8. Testing Strategies", "url": "#8-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 8. Testing Strategies → #8-testing-strategies"}, {"text": "9. Best Practices", "url": "#9-best-practices", "type": "anchor", "status": "Anchor found: 9. Best Practices → #9-best-practices"}, {"text": "10. Navigation", "url": "#10-navigation", "type": "anchor", "status": "Anchor found: 10. Navigation → #10-navigation"}, {"text": "Performance Optimization Guide", "url": "150-performance-optimization-guide.md", "type": "internal", "status": "File exists: frontend/150-performance-optimization-guide.md"}, {"text": "Testing Approaches Guide", "url": "160-testing-approaches-guide.md", "type": "internal", "status": "File exists: frontend/160-testing-approaches-guide.md"}]}, {"file": "frontend/160-testing-approaches-guide.md", "total_links": 13, "internal_links": 2, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Testing Strategy", "url": "#2-testing-strategy", "type": "anchor", "status": "<PERSON><PERSON> found: 2. Testing Strategy → #2-testing-strategy"}, {"text": "3. Unit Testing Volt Components", "url": "#3-unit-testing-volt-components", "type": "anchor", "status": "Anchor found: 3. Unit Testing Volt Components → #3-unit-testing-volt-components"}, {"text": "4. Feature Testing", "url": "#4-feature-testing", "type": "anchor", "status": "Anchor found: 4. Feature Testing → #4-feature-testing"}, {"text": "5. Browser Testing", "url": "#5-browser-testing", "type": "anchor", "status": "Anchor found: 5. <PERSON><PERSON>er Testing → #5-browser-testing"}, {"text": "6. Accessibility Testing", "url": "#6-accessibility-testing", "type": "anchor", "status": "<PERSON><PERSON> found: 6. Accessibility Testing → #6-accessibility-testing"}, {"text": "7. Performance Testing", "url": "#7-performance-testing", "type": "anchor", "status": "Anchor found: 7. Performance Testing → #7-performance-testing"}, {"text": "8. Visual Regression Testing", "url": "#8-visual-regression-testing", "type": "anchor", "status": "Anchor found: 8. Visual Regression Testing → #8-visual-regression-testing"}, {"text": "9. Test Data Management", "url": "#9-test-data-management", "type": "anchor", "status": "Anchor found: 9. Test Data Management → #9-test-data-management"}, {"text": "10. Best Practices", "url": "#10-best-practices", "type": "anchor", "status": "Anchor found: 10. Best Practices → #10-best-practices"}, {"text": "11. <PERSON>", "url": "#11-navigation", "type": "anchor", "status": "Anchor found: 11. <PERSON> → #11-navigation"}, {"text": "Livewire/Volt Integration Guide", "url": "160-livewire-volt-integration-guide.md", "type": "internal", "status": "File exists: frontend/160-livewire-volt-integration-guide.md"}, {"text": "Performance Monitoring Guide", "url": "170-performance-monitoring-guide.md", "type": "internal", "status": "File exists: frontend/170-performance-monitoring-guide.md"}]}, {"file": "frontend/170-performance-monitoring-guide.md", "total_links": 12, "internal_links": 2, "anchor_links": 10, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Monitoring Stack Setup", "url": "#2-monitoring-stack-setup", "type": "anchor", "status": "Anchor found: 2. Monitoring Stack Setup → #2-monitoring-stack-setup"}, {"text": "3. Application Performance Monitoring", "url": "#3-application-performance-monitoring", "type": "anchor", "status": "Anchor found: 3. Application Performance Monitoring → #3-application-performance-monitoring"}, {"text": "4. Database Performance Monitoring", "url": "#4-database-performance-monitoring", "type": "anchor", "status": "Anchor found: 4. Database Performance Monitoring → #4-database-performance-monitoring"}, {"text": "5. Frontend Performance Monitoring", "url": "#5-frontend-performance-monitoring", "type": "anchor", "status": "Anchor found: 5. Frontend Performance Monitoring → #5-frontend-performance-monitoring"}, {"text": "6. Music Streaming Metrics", "url": "#6-music-streaming-metrics", "type": "anchor", "status": "Anchor found: 6. Music Streaming Metrics → #6-music-streaming-metrics"}, {"text": "7. Alerting Systems", "url": "#7-alerting-systems", "type": "anchor", "status": "Anchor found: 7. Alerting Systems → #7-alerting-systems"}, {"text": "8. Dashboard Configuration", "url": "#8-dashboard-configuration", "type": "anchor", "status": "Anchor found: 8. Dashboard Configuration → #8-dashboard-configuration"}, {"text": "9. Best Practices", "url": "#9-best-practices", "type": "anchor", "status": "Anchor found: 9. Best Practices → #9-best-practices"}, {"text": "10. Navigation", "url": "#10-navigation", "type": "anchor", "status": "Anchor found: 10. Navigation → #10-navigation"}, {"text": "Testing Approaches Guide", "url": "160-testing-approaches-guide.md", "type": "internal", "status": "File exists: frontend/160-testing-approaches-guide.md"}, {"text": "API Testing Guide", "url": "180-api-testing-guide.md", "type": "internal", "status": "File exists: frontend/180-api-testing-guide.md"}]}, {"file": "frontend/180-api-testing-guide.md", "total_links": 11, "internal_links": 2, "anchor_links": 9, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. API Testing Strategy", "url": "#2-api-testing-strategy", "type": "anchor", "status": "Anchor found: 2. API Testing Strategy → #2-api-testing-strategy"}, {"text": "3. Music Platform Endpoints", "url": "#3-music-platform-endpoints", "type": "anchor", "status": "Anchor found: 3. Music Platform Endpoints → #3-music-platform-endpoints"}, {"text": "4. Authentication Testing", "url": "#4-authentication-testing", "type": "anchor", "status": "Anchor found: 4. Authentication Testing → #4-authentication-testing"}, {"text": "5. Streaming API Testing", "url": "#5-streaming-api-testing", "type": "anchor", "status": "Anchor found: 5. Streaming API Testing → #5-streaming-api-testing"}, {"text": "6. Performance Testing", "url": "#6-performance-testing", "type": "anchor", "status": "Anchor found: 6. Performance Testing → #6-performance-testing"}, {"text": "7. Security Testing", "url": "#7-security-testing", "type": "anchor", "status": "An<PERSON> found: 7. Security Testing → #7-security-testing"}, {"text": "8. Best Practices", "url": "#8-best-practices", "type": "anchor", "status": "Anchor found: 8. Best Practices → #8-best-practices"}, {"text": "9. <PERSON>", "url": "#9-navigation", "type": "anchor", "status": "Anchor found: 9. <PERSON> → #9-navigation"}, {"text": "Performance Monitoring Guide", "url": "170-performance-monitoring-guide.md", "type": "internal", "status": "File exists: frontend/170-performance-monitoring-guide.md"}, {"text": "CI/CD Integration Guide", "url": "190-cicd-integration-guide.md", "type": "internal", "status": "File exists: frontend/190-cicd-integration-guide.md"}]}, {"file": "frontend/190-cicd-integration-guide.md", "total_links": 12, "internal_links": 2, "anchor_links": 10, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. CI/CD Strategy", "url": "#2-cicd-strategy", "type": "anchor", "status": "Anchor found: 2. CI/CD Strategy → #2-cicd-strategy"}, {"text": "3. GitHub Actions Workflows", "url": "#3-github-actions-workflows", "type": "anchor", "status": "Anchor found: 3. GitHub Actions Workflows → #3-github-actions-workflows"}, {"text": "4. Testing Pipeline", "url": "#4-testing-pipeline", "type": "anchor", "status": "Anchor found: 4. Testing Pipeline → #4-testing-pipeline"}, {"text": "5. Build and Deployment", "url": "#5-build-and-deployment", "type": "anchor", "status": "Anchor found: 5. Build and Deployment → #5-build-and-deployment"}, {"text": "6. Environment Management", "url": "#6-environment-management", "type": "anchor", "status": "Anchor found: 6. Environment Management → #6-environment-management"}, {"text": "7. Security and Secrets", "url": "#7-security-and-secrets", "type": "anchor", "status": "Anchor found: 7. Security and Secrets → #7-security-and-secrets"}, {"text": "8. Best Practices", "url": "#8-best-practices", "type": "anchor", "status": "Anchor found: 8. Best Practices → #8-best-practices"}, {"text": "9. <PERSON><PERSON> Strategies", "url": "#9-rollback-strategies", "type": "anchor", "status": "Anchor found: 9. Roll<PERSON> Strategies → #9-rollback-strategies"}, {"text": "10. Navigation", "url": "#10-navigation", "type": "anchor", "status": "Anchor found: 10. Navigation → #10-navigation"}, {"text": "API Testing Guide", "url": "180-api-testing-guide.md", "type": "internal", "status": "File exists: frontend/180-api-testing-guide.md"}, {"text": "Media Library Enhancement Guide", "url": "200-media-library-enhancement-guide.md", "type": "internal", "status": "File exists: frontend/200-media-library-enhancement-guide.md"}]}, {"file": "frontend/200-media-library-enhancement-guide.md", "total_links": 10, "internal_links": 2, "anchor_links": 8, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Advanced File Upload", "url": "#2-advanced-file-upload", "type": "anchor", "status": "Anchor found: 2. Advanced File Upload → #2-advanced-file-upload"}, {"text": "3. Image Processing", "url": "#3-image-processing", "type": "anchor", "status": "Anchor found: 3. Image Processing → #3-image-processing"}, {"text": "4. Audio File Handling", "url": "#4-audio-file-handling", "type": "anchor", "status": "Anchor found: 4. Audio File Handling → #4-audio-file-handling"}, {"text": "5. Performance Optimization", "url": "#5-performance-optimization", "type": "anchor", "status": "Anchor found: 5. Performance Optimization → #5-performance-optimization"}, {"text": "6. Security Considerations", "url": "#6-security-considerations", "type": "anchor", "status": "<PERSON><PERSON> found: 6. Security Considerations → #6-security-considerations"}, {"text": "7. Best Practices", "url": "#7-best-practices", "type": "anchor", "status": "Anchor found: 7. Best Practices → #7-best-practices"}, {"text": "8. <PERSON>", "url": "#8-navigation", "type": "anchor", "status": "Anchor found: 8. <PERSON> → #8-navigation"}, {"text": "CI/CD Integration Guide", "url": "190-cicd-integration-guide.md", "type": "internal", "status": "File exists: frontend/190-cicd-integration-guide.md"}, {"text": "Frontend Index", "url": "000-frontend-index.md", "type": "internal", "status": "File exists: frontend/000-frontend-index.md"}]}, {"file": "packages/000-packages-index.md", "total_links": 51, "internal_links": 15, "anchor_links": 36, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2. Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2. <PERSON><PERSON> → #12-overview"}, {"text": "2. Package Categories", "url": "#2-package-categories", "type": "anchor", "status": "Anchor found: 2. Package Categories → #2-package-categories"}, {"text": "2.1. Backup & Monitoring", "url": "#21-backup--monitoring", "type": "anchor", "status": "Anchor found: 2.1. Backup & Monitoring → #21-backup--monitoring"}, {"text": "2.2. Performance & Optimization", "url": "#22-performance--optimization", "type": "anchor", "status": "Anchor found: 2.2. Performance & Optimization → #22-performance--optimization"}, {"text": "2.3. API Development", "url": "#23-api-development", "type": "anchor", "status": "Anchor found: 2.3. API Development → #23-api-development"}, {"text": "2.4. Queue Management", "url": "#24-queue-management", "type": "anchor", "status": "Anchor found: 2.4. Queue Management → #24-queue-management"}, {"text": "2.5. Data Transformation", "url": "#25-data-transformation", "type": "anchor", "status": "<PERSON><PERSON> found: 2.5. Data Transformation → #25-data-transformation"}, {"text": "2.6. Enterprise Authentication", "url": "#26-enterprise-authentication", "type": "anchor", "status": "Anchor found: 2.6. Enterprise Authentication → #26-enterprise-authentication"}, {"text": "2.7. Taxonomy Management", "url": "#27-taxonomy-management", "type": "anchor", "status": "Anchor found: 2.7. Taxonomy Management → #27-taxonomy-management"}, {"text": "2.8. User Engagement", "url": "#28-user-engagement", "type": "anchor", "status": "Anchor found: 2.8. User Engagement → #28-user-engagement"}, {"text": "2.9. Geographic Data", "url": "#29-geographic-data", "type": "anchor", "status": "An<PERSON> found: 2.9. Geographic Data → #29-geographic-data"}, {"text": "2.10. Activity Logging", "url": "#210-activity-logging", "type": "anchor", "status": "Anchor found: 2.10. Activity Logging → #210-activity-logging"}, {"text": "3. Implementation Guides", "url": "#3-implementation-guides", "type": "anchor", "status": "Anchor found: 3. Implementation Guides → #3-implementation-guides"}, {"text": "3.1. <PERSON><PERSON>", "url": "#31-<PERSON><PERSON><PERSON>-backup", "type": "anchor", "status": "Anchor found: 3.1. <PERSON><PERSON> → #31-<PERSON><PERSON><PERSON>-backup"}, {"text": "3.2. <PERSON><PERSON>", "url": "#32-laravel-pulse", "type": "anchor", "status": "Anchor found: 3.2. <PERSON><PERSON> → #32-laravel-pulse"}, {"text": "3.3. <PERSON><PERSON> Teles<PERSON>", "url": "#33-laravel-telescope", "type": "anchor", "status": "Anchor found: 3.3. <PERSON><PERSON> Teles<PERSON> → #33-laravel-telescope"}, {"text": "3.4. <PERSON><PERSON> with FrankenPHP", "url": "#34-laravel-octane-with-frankenphp", "type": "anchor", "status": "Anchor found: 3.4. <PERSON><PERSON> Octane with FrankenPHP → #34-laravel-octane-with-frankenphp"}, {"text": "3.5. <PERSON><PERSON>", "url": "#35-laravel-horizon", "type": "anchor", "status": "Anchor found: 3.5. <PERSON><PERSON> → #35-laravel-horizon"}, {"text": "3.6. <PERSON><PERSON>", "url": "#36-laravel-data", "type": "anchor", "status": "Anchor found: 3.6. <PERSON><PERSON> → #36-laravel-data"}, {"text": "3.7. <PERSON><PERSON>", "url": "#37-laravel-fractal", "type": "anchor", "status": "Anchor found: 3.7. <PERSON><PERSON> → #37-laravel-fractal"}, {"text": "3.8. <PERSON><PERSON>", "url": "#38-laravel-sanctum", "type": "anchor", "status": "Anchor found: 3.8. <PERSON><PERSON> → #38-laravel-sanctum"}, {"text": "3.9. <PERSON><PERSON>", "url": "#39-<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "type": "anchor", "status": "Anchor found: 3.9. <PERSON><PERSON> → #39-la<PERSON>l-workos"}, {"text": "3.10. Aliziodev Laravel Taxonomy", "url": "#310-aliziodev-laravel-taxonomy", "type": "anchor", "status": "Anchor found: 3.10. Ali<PERSON><PERSON>v Laravel Taxonomy → #310-aliziodev-laravel-taxonomy"}, {"text": "3.11. Spatie Media Library", "url": "#311-spatie-media-library", "type": "anchor", "status": "Anchor found: 3.11. Spatie Media Library → #311-spatie-media-library"}, {"text": "3.12. <PERSON><PERSON>", "url": "#312-spatie-permission", "type": "anchor", "status": "Anchor found: 3.12. <PERSON><PERSON> → #312-spatie-permission"}, {"text": "3.13. <PERSON><PERSON>", "url": "#313-spatie-comments", "type": "anchor", "status": "An<PERSON> found: 3.13. <PERSON><PERSON> → #313-spatie-comments"}, {"text": "3.14. <PERSON><PERSON> ActivityLog", "url": "#314-spatie-activitylog", "type": "anchor", "status": "An<PERSON> found: 3.14. <PERSON><PERSON> ActivityLog → #314-spatie-activitylog"}, {"text": "4. <PERSON> Patterns", "url": "#4-integration-patterns", "type": "anchor", "status": "An<PERSON> found: 4. <PERSON> Patterns → #4-integration-patterns"}, {"text": "4.1. Monitoring Stack Integration", "url": "#41-monitoring-stack-integration", "type": "anchor", "status": "Anchor found: 4.1. Monitoring Stack Integration → #41-monitoring-stack-integration"}, {"text": "4.2. Performance Optimization Stack", "url": "#42-performance-optimization-stack", "type": "anchor", "status": "Anchor found: 4.2. Performance Optimization Stack → #42-performance-optimization-stack"}, {"text": "4.3. Development & Production Workflow", "url": "#43-development--production-workflow", "type": "anchor", "status": "Anchor found: 4.3. Development & Production Workflow → #43-development--production-workflow"}, {"text": "5. Best Practices", "url": "#5-best-practices", "type": "anchor", "status": "Anchor found: 5. Best Practices → #5-best-practices"}, {"text": "5.1. Installation & Configuration", "url": "#51-installation--configuration", "type": "anchor", "status": "Anchor found: 5.1. Installation & Configuration → #51-installation--configuration"}, {"text": "5.2. Monitoring & Maintenance", "url": "#52-monitoring--maintenance", "type": "anchor", "status": "Anchor found: 5.2. Monitoring & Maintenance → #52-monitoring--maintenance"}, {"text": "5.3. Team Collaboration", "url": "#53-team-collaboration", "type": "anchor", "status": "Anchor found: 5.3. Team Collaboration → #53-team-collaboration"}, {"text": "010-la<PERSON>l-backup-guide.md", "url": "010-la<PERSON>l-backup-guide.md", "type": "internal", "status": "File exists: packages/010-laravel-backup-guide.md"}, {"text": "020-laravel-pulse-guide.md", "url": "020-laravel-pulse-guide.md", "type": "internal", "status": "File exists: packages/020-laravel-pulse-guide.md"}, {"text": "030-laravel-telescope-guide.md", "url": "030-laravel-telescope-guide.md", "type": "internal", "status": "File exists: packages/030-laravel-telescope-guide.md"}, {"text": "040-laravel-octane-frankenphp-guide.md", "url": "040-laravel-octane-frankenphp-guide.md", "type": "internal", "status": "File exists: packages/040-laravel-octane-frankenphp-guide.md"}, {"text": "050-laravel-horizon-guide.md", "url": "050-laravel-horizon-guide.md", "type": "internal", "status": "File exists: packages/050-laravel-horizon-guide.md"}, {"text": "060-laravel-data-guide.md", "url": "060-laravel-data-guide.md", "type": "internal", "status": "File exists: packages/060-laravel-data-guide.md"}, {"text": "070-laravel-fractal-guide.md", "url": "070-laravel-fractal-guide.md", "type": "internal", "status": "File exists: packages/070-laravel-fractal-guide.md"}, {"text": "080-laravel-sanctum-guide.md", "url": "080-laravel-sanctum-guide.md", "type": "internal", "status": "File exists: packages/080-laravel-sanctum-guide.md"}, {"text": "090-laravel-workos-guide.md", "url": "090-laravel-workos-guide.md", "type": "internal", "status": "File exists: packages/090-laravel-workos-guide.md"}, {"text": "110-aliziodev-laravel-taxonomy-guide.md", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "120-spatie-media-library-guide.md", "url": "120-spatie-media-library-guide.md", "type": "internal", "status": "File exists: packages/120-spatie-media-library-guide.md"}, {"text": "140-spatie-permission-guide.md", "url": "140-spatie-permission-guide.md", "type": "internal", "status": "File exists: packages/140-spatie-permission-guide.md"}, {"text": "150-spatie-comments-guide.md", "url": "150-spatie-comments-guide.md", "type": "internal", "status": "File exists: packages/150-spatie-comments-guide.md"}, {"text": "160-spatie-activitylog-guide.md", "url": "160-spatie-activitylog-guide.md", "type": "internal", "status": "File exists: packages/160-spatie-activitylog-guide.md"}, {"text": "Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1. Table of Contents → #11-table-of-contents"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}]}, {"file": "packages/010-laravel-backup-guide.md", "total_links": 10, "internal_links": 2, "anchor_links": 8, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Installation & Setup", "url": "#13-installation--setup", "type": "anchor", "status": "Anchor found: 1.3 Installation & Setup → #13-installation--setup"}, {"text": "1.4 Storage Configuration", "url": "#14-storage-configuration", "type": "anchor", "status": "Anchor found: 1.4 Storage Configuration → #14-storage-configuration"}, {"text": "1.5 Backup Configuration", "url": "#15-backup-configuration", "type": "anchor", "status": "Anchor found: 1.5 Backup Configuration → #15-backup-configuration"}, {"text": "1.6 Automated Scheduling", "url": "#16-automated-scheduling", "type": "anchor", "status": "Anchor found: 1.6 Automated Scheduling → #16-automated-scheduling"}, {"text": "1.7 Notification Setup", "url": "#17-notification-setup", "type": "anchor", "status": "Anchor found: 1.7 Notification Setup → #17-notification-setup"}, {"text": "1.8 Monitoring & Health Checks", "url": "#18-monitoring--health-checks", "type": "anchor", "status": "Anchor found: 1.8 Monitoring & Health Checks → #18-monitoring--health-checks"}, {"text": "1.9 Restoration Procedures", "url": "#19-restoration-procedures", "type": "anchor", "status": "Anchor found: 1.9 Restoration Procedures → #19-restoration-procedures"}, {"text": "<PERSON><PERSON>", "url": "020-laravel-pulse-guide.md", "type": "internal", "status": "File exists: packages/020-laravel-pulse-guide.md"}, {"text": "Package Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}]}, {"file": "packages/020-laravel-pulse-guide.md", "total_links": 42, "internal_links": 2, "anchor_links": 40, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. <PERSON>vel Pulse Implementation Guide", "url": "#1-laravel-pulse-implementation-guide", "type": "anchor", "status": "Anchor found: 1. <PERSON><PERSON> Pulse Implementation Guide → #1-laravel-pulse-implementation-guide"}, {"text": "Table of Contents", "url": "#table-of-contents", "type": "anchor", "status": "Anchor found: Table of Contents → #table-of-contents"}, {"text": "1.1. Overview", "url": "#11-overview", "type": "anchor", "status": "Anchor found: 1.1. <PERSON><PERSON> → #11-overview"}, {"text": "1.2. Installation & Setup", "url": "#12-installation--setup", "type": "anchor", "status": "Anchor found: 1.2. Installation & Setup → #12-installation--setup"}, {"text": "1.2.1. Package Installation", "url": "#121-package-installation", "type": "anchor", "status": "Anchor found: 1.2.1. Package Installation → #121-package-installation"}, {"text": "1.2.2. Database Configuration", "url": "#122-database-configuration", "type": "anchor", "status": "Anchor found: 1.2.2. Database Configuration → #122-database-configuration"}, {"text": "1.2.3. Environment Setup", "url": "#123-environment-setup", "type": "anchor", "status": "Anchor found: 1.2.3. Environment Setup → #123-environment-setup"}, {"text": "1.3. Dashboard Configuration", "url": "#13-dashboard-configuration", "type": "anchor", "status": "Anchor found: 1.3. Dashboard Configuration → #13-dashboard-configuration"}, {"text": "1.3.1. Basic Dashboard Setup", "url": "#131-basic-dashboard-setup", "type": "anchor", "status": "Anchor found: 1.3.1. Basic Dashboard Setup → #131-basic-dashboard-setup"}, {"text": "1.3.2. Custom Dashboard Layouts", "url": "#132-custom-dashboard-layouts", "type": "anchor", "status": "Anchor found: 1.3.2. Custom Dashboard Layouts → #132-custom-dashboard-layouts"}, {"text": "1.3.3. Authentication & Authorization", "url": "#133-authentication--authorization", "type": "anchor", "status": "Anchor found: 1.3.3. Authentication & Authorization → #133-authentication--authorization"}, {"text": "1.4. Data Collection Setup", "url": "#14-data-collection-setup", "type": "anchor", "status": "Anchor found: 1.4. Data Collection Setup → #14-data-collection-setup"}, {"text": "1.4.1. Built-in Recorders", "url": "#141-built-in-recorders", "type": "anchor", "status": "Anchor found: 1.4.1. Built-in Recorders → #141-built-in-recorders"}, {"text": "1.4.2. Custom Metrics Collection", "url": "#142-custom-metrics-collection", "type": "anchor", "status": "Anchor found: 1.4.2. Custom Metrics Collection → #142-custom-metrics-collection"}, {"text": "1.4.3. Performance Monitoring", "url": "#143-performance-monitoring", "type": "anchor", "status": "Anchor found: 1.4.3. Performance Monitoring → #143-performance-monitoring"}, {"text": "1.5. Custom Metrics & Cards", "url": "#15-custom-metrics--cards", "type": "anchor", "status": "Anchor found: 1.5. Custom Metrics & Cards → #15-custom-metrics--cards"}, {"text": "1.5.1. Creating Custom Recorders", "url": "#151-creating-custom-recorders", "type": "anchor", "status": "Anchor found: 1.5.1. Creating Custom Recorders → #151-creating-custom-recorders"}, {"text": "1.5.2. Building Custom Cards", "url": "#152-building-custom-cards", "type": "anchor", "status": "Anchor found: 1.5.2. Building Custom Cards → #152-building-custom-cards"}, {"text": "1.5.3. Business Metrics Integration", "url": "#153-business-metrics-integration", "type": "anchor", "status": "<PERSON><PERSON> found: 1.5.3. Business Metrics Integration → #153-business-metrics-integration"}, {"text": "1.6. Taxonomy Integration", "url": "#16-taxonomy-integration", "type": "anchor", "status": "<PERSON><PERSON> found: 1.6. Taxonomy Integration → #16-taxonomy-integration"}, {"text": "1.6.1. Monitoring Taxonomy Operations", "url": "#161-monitoring-taxonomy-operations", "type": "anchor", "status": "Anchor found: 1.6.1. Monitoring Taxonomy Operations → #161-monitoring-taxonomy-operations"}, {"text": "1.6.2. Taxonomy Performance Metrics", "url": "#162-taxonomy-performance-metrics", "type": "anchor", "status": "<PERSON><PERSON> found: 1.6.2. Taxonomy Performance Metrics → #162-taxonomy-performance-metrics"}, {"text": "1.6.3. Taxonomy-Based Alerting", "url": "#163-taxonomy-based-alerting", "type": "anchor", "status": "An<PERSON> found: 1.6.3. Taxonomy-Based Alerting → #163-taxonomy-based-alerting"}, {"text": "1.7. Performance Optimization", "url": "#17-performance-optimization", "type": "anchor", "status": "Anchor found: 1.7. Performance Optimization → #17-performance-optimization"}, {"text": "1.7.1. Database Optimization", "url": "#171-database-optimization", "type": "anchor", "status": "An<PERSON> found: 1.7.1. Database Optimization → #171-database-optimization"}, {"text": "1.7.2. Caching Strategies", "url": "#172-caching-strategies", "type": "anchor", "status": "Anchor found: 1.7.2. Caching Strategies → #172-caching-strategies"}, {"text": "1.7.3. Sampling Configuration", "url": "#173-sampling-configuration", "type": "anchor", "status": "Anchor found: 1.7.3. Sampling Configuration → #173-sampling-configuration"}, {"text": "1.8. Integration Strategies", "url": "#18-integration-strategies", "type": "anchor", "status": "<PERSON><PERSON> found: 1.8. Integration Strategies → #18-integration-strategies"}, {"text": "1.8.1. Laravel Horizon Integration", "url": "#181-laravel-horizon-integration", "type": "anchor", "status": "<PERSON><PERSON> found: 1.8.1. <PERSON>vel Horizon Integration → #181-laravel-horizon-integration"}, {"text": "1.8.2. External Monitoring Integration", "url": "#182-external-monitoring-integration", "type": "anchor", "status": "An<PERSON> found: 1.8.2. External Monitoring Integration → #182-external-monitoring-integration"}, {"text": "1.8.3. <PERSON>ert <PERSON>", "url": "#183-alert-integration", "type": "anchor", "status": "An<PERSON> found: 1.8.3. Alert Integration → #183-alert-integration"}, {"text": "1.9. Best Practices", "url": "#19-best-practices", "type": "anchor", "status": "Anchor found: 1.9. Best Practices → #19-best-practices"}, {"text": "1.9.1. Data Retention Strategy", "url": "#191-data-retention-strategy", "type": "anchor", "status": "<PERSON><PERSON> found: 1.9.1. Data Retention Strategy → #191-data-retention-strategy"}, {"text": "1.9.2. Security Considerations", "url": "#192-security-considerations", "type": "anchor", "status": "<PERSON><PERSON> found: 1.9.2. Security Considerations → #192-security-considerations"}, {"text": "1.9.3. Performance Monitoring", "url": "#193-performance-monitoring", "type": "anchor", "status": "Anchor found: 1.9.3. Performance Monitoring → #193-performance-monitoring"}, {"text": "1.10. <PERSON>shooting", "url": "#110-troubleshooting", "type": "anchor", "status": "Anchor found: 1.10. Troubleshooting → #110-troubleshooting"}, {"text": "1.10.1. Common Issues", "url": "#1101-common-issues", "type": "anchor", "status": "Anchor found: 1.10.1. Common Issues → #1101-common-issues"}, {"text": "1.10.2. Debug Mode", "url": "#1102-debug-mode", "type": "anchor", "status": "Anchor found: 1.10.2. Debug Mode → #1102-debug-mode"}, {"text": "1.10.3. Performance Tuning", "url": "#1103-performance-tuning", "type": "anchor", "status": "Anchor found: 1.10.3. Performance Tuning → #1103-performance-tuning"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation → #navigation"}, {"text": "<PERSON><PERSON>up Guide", "url": "010-la<PERSON>l-backup-guide.md", "type": "internal", "status": "File exists: packages/010-laravel-backup-guide.md"}, {"text": "Laravel Telescope Guide", "url": "030-laravel-telescope-guide.md", "type": "internal", "status": "File exists: packages/030-laravel-telescope-guide.md"}]}, {"file": "packages/030-laravel-telescope-guide.md", "total_links": 45, "internal_links": 2, "anchor_links": 43, "external_links": 0, "broken_links": [{"text": "Laravel Horizon Guide", "url": "040-laravel-horizon-guide.md", "type": "internal", "status": "File not found: packages/040-laravel-horizon-guide.md"}], "working_links": [{"text": "1. Laravel Telescope Implementation Guide", "url": "#1-laravel-telescope-implementation-guide", "type": "anchor", "status": "Anchor found: 1. <PERSON><PERSON> Telescope Implementation Guide → #1-laravel-telescope-implementation-guide"}, {"text": "Table of Contents", "url": "#table-of-contents", "type": "anchor", "status": "Anchor found: Table of Contents → #table-of-contents"}, {"text": "1.1. Overview", "url": "#11-overview", "type": "anchor", "status": "Anchor found: 1.1. <PERSON><PERSON> → #11-overview"}, {"text": "1.2. Installation & Setup", "url": "#12-installation--setup", "type": "anchor", "status": "Anchor found: 1.2. Installation & Setup → #12-installation--setup"}, {"text": "1.2.1. Package Installation", "url": "#121-package-installation", "type": "anchor", "status": "Anchor found: 1.2.1. Package Installation → #121-package-installation"}, {"text": "1.2.2. Configuration Publishing", "url": "#122-configuration-publishing", "type": "anchor", "status": "Anchor found: 1.2.2. Configuration Publishing → #122-configuration-publishing"}, {"text": "1.2.3. Environment Configuration", "url": "#123-environment-configuration", "type": "anchor", "status": "Anchor found: 1.2.3. Environment Configuration → #123-environment-configuration"}, {"text": "1.3. Authorization & Security", "url": "#13-authorization--security", "type": "anchor", "status": "Anchor found: 1.3. Authorization & Security → #13-authorization--security"}, {"text": "1.3.1. Gate-Based Authorization", "url": "#131-gate-based-authorization", "type": "anchor", "status": "Anchor found: 1.3.1. Gate-Based Authorization → #131-gate-based-authorization"}, {"text": "1.3.2. Environment-Specific Access", "url": "#132-environment-specific-access", "type": "anchor", "status": "An<PERSON> found: 1.3.2. Environment-Specific Access → #132-environment-specific-access"}, {"text": "1.3.3. IP Whitelisting", "url": "#133-ip-whitelisting", "type": "anchor", "status": "Anchor found: 1.3.3. IP Whitelisting → #133-ip-whitelisting"}, {"text": "1.4. Data Collection Configuration", "url": "#14-data-collection-configuration", "type": "anchor", "status": "Anchor found: 1.4. Data Collection Configuration → #14-data-collection-configuration"}, {"text": "1.4.1. Watcher Configuration", "url": "#141-watcher-configuration", "type": "anchor", "status": "Anchor found: 1.4.1. Watcher Configuration → #141-watcher-configuration"}, {"text": "1.4.2. <PERSON><PERSON><PERSON> & Sam<PERSON>", "url": "#142-filtering--sampling", "type": "anchor", "status": "Anchor found: 1.4.2. <PERSON><PERSON><PERSON> & Sam<PERSON> → #142-filtering--sampling"}, {"text": "1.4.3. Performance Impact Mitigation", "url": "#143-performance-impact-mitigation", "type": "anchor", "status": "Anchor found: 1.4.3. Performance Impact Mitigation → #143-performance-impact-mitigation"}, {"text": "1.5. Taxonomy Debugging Integration", "url": "#15-taxonomy-debugging-integration", "type": "anchor", "status": "An<PERSON> found: 1.5. Taxonomy Debugging Integration → #15-taxonomy-debugging-integration"}, {"text": "1.5.1. Taxonomy Query Analysis", "url": "#151-taxonomy-query-analysis", "type": "anchor", "status": "<PERSON><PERSON> found: 1.5.1. Taxonomy Query Analysis → #151-taxonomy-query-analysis"}, {"text": "1.5.2. Taxonomy Performance Monitoring", "url": "#152-taxonomy-performance-monitoring", "type": "anchor", "status": "<PERSON><PERSON> found: 1.5.2. Taxonomy Performance Monitoring → #152-taxonomy-performance-monitoring"}, {"text": "1.5.3. Taxonomy Exception Tracking", "url": "#153-taxonomy-exception-tracking", "type": "anchor", "status": "An<PERSON> found: 1.5.3. Taxonomy Exception Tracking → #153-taxonomy-exception-tracking"}, {"text": "1.6. Data Pruning & Storage Management", "url": "#16-data-pruning--storage-management", "type": "anchor", "status": "Anchor found: 1.6. Data Pruning & Storage Management → #16-data-pruning--storage-management"}, {"text": "1.6.1. Automated Pruning", "url": "#161-automated-pruning", "type": "anchor", "status": "Anchor found: 1.6.1. Automated Pruning → #161-automated-pruning"}, {"text": "1.6.2. Storage Optimization", "url": "#162-storage-optimization", "type": "anchor", "status": "Anchor found: 1.6.2. Storage Optimization → #162-storage-optimization"}, {"text": "1.6.3. Custom Retention Policies", "url": "#163-custom-retention-policies", "type": "anchor", "status": "Anchor found: 1.6.3. Custom Retention Policies → #163-custom-retention-policies"}, {"text": "1.7. Debugging Workflows", "url": "#17-debugging-workflows", "type": "anchor", "status": "Anchor found: 1.7. Debugging Workflows → #17-debugging-workflows"}, {"text": "1.7.1. Request Debugging", "url": "#171-request-debugging", "type": "anchor", "status": "Anchor found: 1.7.1. Request Debugging → #171-request-debugging"}, {"text": "1.7.2. Database Query Analysis", "url": "#172-database-query-analysis", "type": "anchor", "status": "Anchor found: 1.7.2. Database Query Analysis → #172-database-query-analysis"}, {"text": "1.7.3. Exception Analysis", "url": "#173-exception-analysis", "type": "anchor", "status": "An<PERSON> found: 1.7.3. Exception Analysis → #173-exception-analysis"}, {"text": "1.8. Production Considerations", "url": "#18-production-considerations", "type": "anchor", "status": "Anchor found: 1.8. Production Considerations → #18-production-considerations"}, {"text": "1.8.1. Performance Impact Assessment", "url": "#181-performance-impact-assessment", "type": "anchor", "status": "<PERSON><PERSON> found: 1.8.1. Performance Impact Assessment → #181-performance-impact-assessment"}, {"text": "1.8.2. Security Hardening", "url": "#182-security-hardening", "type": "anchor", "status": "Anchor found: 1.8.2. Security Hardening → #182-security-hardening"}, {"text": "1.8.3. Data Sanitization", "url": "#183-data-sanitization", "type": "anchor", "status": "<PERSON><PERSON> found: 1.8.3. Data Sanitization → #183-data-sanitization"}, {"text": "1.9. Integration Strategies", "url": "#19-integration-strategies", "type": "anchor", "status": "<PERSON><PERSON> found: 1.9. Integration Strategies → #19-integration-strategies"}, {"text": "1.9.1. <PERSON><PERSON>", "url": "#191-laravel-pulse-integration", "type": "anchor", "status": "Anchor found: 1.9.1. <PERSON><PERSON> Pulse Integration → #191-laravel-pulse-integration"}, {"text": "1.9.2. External Monitoring Integration", "url": "#192-external-monitoring-integration", "type": "anchor", "status": "Anchor found: 1.9.2. External Monitoring Integration → #192-external-monitoring-integration"}, {"text": "1.10. Best Practices", "url": "#110-best-practices", "type": "anchor", "status": "Anchor found: 1.10. Best Practices → #110-best-practices"}, {"text": "1.10.1. Development Workflow", "url": "#1101-development-workflow", "type": "anchor", "status": "Anchor found: 1.10.1. Development Workflow → #1101-development-workflow"}, {"text": "1.10.2. Team Collaboration", "url": "#1102-team-collaboration", "type": "anchor", "status": "Anchor found: 1.10.2. Team Collaboration → #1102-team-collaboration"}, {"text": "1.10.3. Performance Monitoring", "url": "#1103-performance-monitoring", "type": "anchor", "status": "Anchor found: 1.10.3. Performance Monitoring → #1103-performance-monitoring"}, {"text": "1.11. Troubleshooting", "url": "#111-troubleshooting", "type": "anchor", "status": "Anchor found: 1.11. Troubleshooting → #111-troubleshooting"}, {"text": "1.11.1. Common Issues", "url": "#1111-common-issues", "type": "anchor", "status": "Anchor found: 1.11.1. Common Issues → #1111-common-issues"}, {"text": "1.11.2. Debug Mode", "url": "#1112-debug-mode", "type": "anchor", "status": "Anchor found: 1.11.2. Debug Mode → #1112-debug-mode"}, {"text": "1.11.3. Data Recovery", "url": "#1113-data-recovery", "type": "anchor", "status": "An<PERSON> found: 1.11.3. Data Recovery → #1113-data-recovery"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation → #navigation"}, {"text": "<PERSON><PERSON>", "url": "020-laravel-pulse-guide.md", "type": "internal", "status": "File exists: packages/020-laravel-pulse-guide.md"}]}, {"file": "packages/040-laravel-octane-frankenphp-guide.md", "total_links": 40, "internal_links": 2, "anchor_links": 38, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. <PERSON>vel Octane with FrankenPHP Implementation Guide", "url": "#1-laravel-octane-with-frankenphp-implementation-guide", "type": "anchor", "status": "Anchor found: 1. <PERSON>vel Octane with FrankenPHP Implementation Guide → #1-laravel-octane-with-frankenphp-implementation-guide"}, {"text": "Table of Contents", "url": "#table-of-contents", "type": "anchor", "status": "Anchor found: Table of Contents → #table-of-contents"}, {"text": "1.1. Overview", "url": "#11-overview", "type": "anchor", "status": "Anchor found: 1.1. <PERSON><PERSON> → #11-overview"}, {"text": "1.2. Installation & Setup", "url": "#12-installation--setup", "type": "anchor", "status": "Anchor found: 1.2. Installation & Setup → #12-installation--setup"}, {"text": "1.2.1. Package Installation", "url": "#121-package-installation", "type": "anchor", "status": "Anchor found: 1.2.1. Package Installation → #121-package-installation"}, {"text": "1.2.2. FrankenPHP Server Setup", "url": "#122-frankenphp-server-setup", "type": "anchor", "status": "Anchor found: 1.2.2. FrankenPHP Server Setup → #122-frankenphp-server-setup"}, {"text": "1.2.3. Environment Configuration", "url": "#123-environment-configuration", "type": "anchor", "status": "Anchor found: 1.2.3. Environment Configuration → #123-environment-configuration"}, {"text": "1.3. Server Configuration", "url": "#13-server-configuration", "type": "anchor", "status": "Anchor found: 1.3. Server Configuration → #13-server-configuration"}, {"text": "1.3.1. Basic Server Settings", "url": "#131-basic-server-settings", "type": "anchor", "status": "Anchor found: 1.3.1. Basic Server Settings → #131-basic-server-settings"}, {"text": "1.3.2. Performance Optimization", "url": "#132-performance-optimization", "type": "anchor", "status": "Anchor found: 1.3.2. Performance Optimization → #132-performance-optimization"}, {"text": "1.3.3. SSL/TLS Configuration", "url": "#133-ssltls-configuration", "type": "anchor", "status": "Anchor found: 1.3.3. SSL/TLS Configuration → #133-ssltls-configuration"}, {"text": "1.4. Memory Management", "url": "#14-memory-management", "type": "anchor", "status": "Anchor found: 1.4. Memory Management → #14-memory-management"}, {"text": "1.4.1. Memory Leak Prevention", "url": "#141-memory-leak-prevention", "type": "anchor", "status": "Anchor found: 1.4.1. Memory Leak Prevention → #141-memory-leak-prevention"}, {"text": "1.4.2. Resource Optimization", "url": "#142-resource-optimization", "type": "anchor", "status": "<PERSON><PERSON> found: 1.4.2. Resource Optimization → #142-resource-optimization"}, {"text": "1.4.3. Garbage Collection Tuning", "url": "#143-garbage-collection-tuning", "type": "anchor", "status": "Anchor found: 1.4.3. Garbage Collection Tuning → #143-garbage-collection-tuning"}, {"text": "1.5. Taxonomy Performance Optimization", "url": "#15-taxonomy-performance-optimization", "type": "anchor", "status": "An<PERSON> found: 1.5. Taxonomy Performance Optimization → #15-taxonomy-performance-optimization"}, {"text": "1.5.1. Taxonomy Caching Strategies", "url": "#151-taxonomy-caching-strategies", "type": "anchor", "status": "Anchor found: 1.5.1. Taxonomy Caching Strategies → #151-taxonomy-caching-strategies"}, {"text": "1.5.2. Taxonomy Query Optimization", "url": "#152-taxonomy-query-optimization", "type": "anchor", "status": "An<PERSON> found: 1.5.2. Taxonomy Query Optimization → #152-taxonomy-query-optimization"}, {"text": "1.5.3. Taxonomy Memory Management", "url": "#153-taxonomy-memory-management", "type": "anchor", "status": "Anchor found: 1.5.3. Taxonomy Memory Management → #153-taxonomy-memory-management"}, {"text": "1.6. Production Deployment", "url": "#16-production-deployment", "type": "anchor", "status": "Anchor found: 1.6. Production Deployment → #16-production-deployment"}, {"text": "1.6.1. <PERSON><PERSON> Configuration", "url": "#161-docker-configuration", "type": "anchor", "status": "Anchor found: 1.6.1. Docker Configuration → #161-docker-configuration"}, {"text": "1.6.2. <PERSON><PERSON>", "url": "#162-load-balancing", "type": "anchor", "status": "Anchor found: 1.6.2. <PERSON><PERSON> → #162-load-balancing"}, {"text": "1.6.3. Scaling Strategies", "url": "#163-scaling-strategies", "type": "anchor", "status": "An<PERSON> found: 1.6.3. Scaling Strategies → #163-scaling-strategies"}, {"text": "1.7. Monitoring & Troubleshooting", "url": "#17-monitoring--troubleshooting", "type": "anchor", "status": "Anchor found: 1.7. Monitoring & Troubleshooting → #17-monitoring--troubleshooting"}, {"text": "1.7.1. Performance Monitoring", "url": "#171-performance-monitoring", "type": "anchor", "status": "Anchor found: 1.7.1. Performance Monitoring → #171-performance-monitoring"}, {"text": "1.7.2. Health Checks", "url": "#172-health-checks", "type": "anchor", "status": "Anchor found: 1.7.2. Health Checks → #172-health-checks"}, {"text": "1.7.3. Debugging Tools", "url": "#173-debugging-tools", "type": "anchor", "status": "Anchor found: 1.7.3. Debugging Tools → #173-debugging-tools"}, {"text": "1.8. Integration Strategies", "url": "#18-integration-strategies", "type": "anchor", "status": "<PERSON><PERSON> found: 1.8. Integration Strategies → #18-integration-strategies"}, {"text": "1.8.1. <PERSON><PERSON>", "url": "#181-laravel-pulse-integration", "type": "anchor", "status": "Anchor found: 1.8.1. <PERSON><PERSON> Pulse Integration → #181-laravel-pulse-integration"}, {"text": "1.8.2. External Monitoring Integration", "url": "#182-external-monitoring-integration", "type": "anchor", "status": "An<PERSON> found: 1.8.2. External Monitoring Integration → #182-external-monitoring-integration"}, {"text": "1.9. Best Practices", "url": "#19-best-practices", "type": "anchor", "status": "Anchor found: 1.9. Best Practices → #19-best-practices"}, {"text": "1.9.1. Development Workflow", "url": "#191-development-workflow", "type": "anchor", "status": "Anchor found: 1.9.1. Development Workflow → #191-development-workflow"}, {"text": "1.9.2. Production Optimization", "url": "#192-production-optimization", "type": "anchor", "status": "Anchor found: 1.9.2. Production Optimization → #192-production-optimization"}, {"text": "1.9.3. Security Hardening", "url": "#193-security-hardening", "type": "anchor", "status": "Anchor found: 1.9.3. Security Hardening → #193-security-hardening"}, {"text": "1.10. Performance Benchmarks", "url": "#110-performance-benchmarks", "type": "anchor", "status": "Anchor found: 1.10. Performance Benchmarks → #110-performance-benchmarks"}, {"text": "1.10.1. <PERSON><PERSON><PERSON> Results", "url": "#1101-benchmark-results", "type": "anchor", "status": "Anchor found: 1.10.1. Benchmark Results → #1101-benchmark-results"}, {"text": "1.10.2. Load Testing", "url": "#1102-load-testing", "type": "anchor", "status": "Anchor found: 1.10.2. Load Testing → #1102-load-testing"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation → #navigation"}, {"text": "Laravel Telescope Guide", "url": "030-laravel-telescope-guide.md", "type": "internal", "status": "File exists: packages/030-laravel-telescope-guide.md"}, {"text": "Laravel Horizon Guide", "url": "050-laravel-horizon-guide.md", "type": "internal", "status": "File exists: packages/050-laravel-horizon-guide.md"}]}, {"file": "packages/050-laravel-horizon-guide.md", "total_links": 46, "internal_links": 2, "anchor_links": 44, "external_links": 0, "broken_links": [{"text": "Laravel Telescope Guide", "url": "060-laravel-telescope-guide.md", "type": "internal", "status": "File not found: packages/060-laravel-telescope-guide.md"}], "working_links": [{"text": "1. Laravel Horizon Implementation Guide", "url": "#1-laravel-horizon-implementation-guide", "type": "anchor", "status": "Anchor found: 1. Laravel Horizon Implementation Guide → #1-laravel-horizon-implementation-guide"}, {"text": "Table of Contents", "url": "#table-of-contents", "type": "anchor", "status": "Anchor found: Table of Contents → #table-of-contents"}, {"text": "1.1. Overview", "url": "#11-overview", "type": "anchor", "status": "Anchor found: 1.1. <PERSON><PERSON> → #11-overview"}, {"text": "1.2. Installation & Setup", "url": "#12-installation--setup", "type": "anchor", "status": "Anchor found: 1.2. Installation & Setup → #12-installation--setup"}, {"text": "1.2.1. Package Installation", "url": "#121-package-installation", "type": "anchor", "status": "Anchor found: 1.2.1. Package Installation → #121-package-installation"}, {"text": "1.2.2. Configuration Publishing", "url": "#122-configuration-publishing", "type": "anchor", "status": "Anchor found: 1.2.2. Configuration Publishing → #122-configuration-publishing"}, {"text": "1.2.3. Environment Setup", "url": "#123-environment-setup", "type": "anchor", "status": "Anchor found: 1.2.3. Environment Setup → #123-environment-setup"}, {"text": "1.3. Dashboard Configuration", "url": "#13-dashboard-configuration", "type": "anchor", "status": "Anchor found: 1.3. Dashboard Configuration → #13-dashboard-configuration"}, {"text": "1.3.1. Basic Dashboard Setup", "url": "#131-basic-dashboard-setup", "type": "anchor", "status": "Anchor found: 1.3.1. Basic Dashboard Setup → #131-basic-dashboard-setup"}, {"text": "1.3.2. Authentication & Authorization", "url": "#132-authentication--authorization", "type": "anchor", "status": "Anchor found: 1.3.2. Authentication & Authorization → #132-authentication--authorization"}, {"text": "1.3.3. Custom Dashboard Views", "url": "#133-custom-dashboard-views", "type": "anchor", "status": "Anchor found: 1.3.3. Custom Dashboard Views → #133-custom-dashboard-views"}, {"text": "1.4. Worker Configuration", "url": "#14-worker-configuration", "type": "anchor", "status": "Anchor found: 1.4. Worker Configuration → #14-worker-configuration"}, {"text": "1.4.1. <PERSON><PERSON> Worker Settings", "url": "#141-queue-worker-settings", "type": "anchor", "status": "Anchor found: 1.4.1. Queue Worker Settings → #141-queue-worker-settings"}, {"text": "1.4.2. Supervisor Configuration", "url": "#142-supervisor-configuration", "type": "anchor", "status": "Anchor found: 1.4.2. Supervisor Configuration → #142-supervisor-configuration"}, {"text": "1.4.3. Auto-Scaling Setup", "url": "#143-auto-scaling-setup", "type": "anchor", "status": "Anchor found: 1.4.3. Auto-Scaling Setup → #143-auto-scaling-setup"}, {"text": "1.5. Taxonomy Queue Management", "url": "#15-taxonomy-queue-management", "type": "anchor", "status": "Anchor found: 1.5. Taxonomy Queue Management → #15-taxonomy-queue-management"}, {"text": "1.5.1. Taxonomy Job Processing", "url": "#151-taxonomy-job-processing", "type": "anchor", "status": "<PERSON><PERSON> found: 1.5.1. Taxonomy Job Processing → #151-taxonomy-job-processing"}, {"text": "1.5.2. Taxonomy Queue Optimization", "url": "#152-taxonomy-queue-optimization", "type": "anchor", "status": "<PERSON><PERSON> found: 1.5.2. Taxonomy Queue Optimization → #152-taxonomy-queue-optimization"}, {"text": "1.5.3. Taxonomy Performance Monitoring", "url": "#153-taxonomy-performance-monitoring", "type": "anchor", "status": "<PERSON><PERSON> found: 1.5.3. Taxonomy Performance Monitoring → #153-taxonomy-performance-monitoring"}, {"text": "1.6. Enhanced Monitoring", "url": "#16-enhanced-monitoring", "type": "anchor", "status": "Anchor found: 1.6. Enhanced Monitoring → #16-enhanced-monitoring"}, {"text": "1.6.1. Horizon Watcher Integration", "url": "#161-horizon-watcher-integration", "type": "anchor", "status": "An<PERSON> found: 1.6.1. Horizon Watcher Integration → #161-horizon-watcher-integration"}, {"text": "1.6.2. Custom Metrics Collection", "url": "#162-custom-metrics-collection", "type": "anchor", "status": "Anchor found: 1.6.2. Custom Metrics Collection → #162-custom-metrics-collection"}, {"text": "1.6.3. <PERSON><PERSON>figu<PERSON>", "url": "#163-alert-configuration", "type": "anchor", "status": "Anchor found: 1.6.3. <PERSON><PERSON> Configuration → #163-alert-configuration"}, {"text": "1.7. Deployment Procedures", "url": "#17-deployment-procedures", "type": "anchor", "status": "Anchor found: 1.7. Deployment Procedures → #17-deployment-procedures"}, {"text": "1.7.1. Zero-Downtime Deployment", "url": "#171-zero-downtime-deployment", "type": "anchor", "status": "Anchor found: 1.7.1. Zero-Downtime Deployment → #171-zero-downtime-deployment"}, {"text": "1.7.2. Blue-Green Deployment", "url": "#172-blue-green-deployment", "type": "anchor", "status": "Anchor found: 1.7.2. Blue-Green Deployment → #172-blue-green-deployment"}, {"text": "1.7.3. <PERSON><PERSON> Procedures", "url": "#173-rollback-procedures", "type": "anchor", "status": "Anchor found: 1.7.3. Rollback Procedures → #173-rollback-procedures"}, {"text": "1.8. Performance Tuning", "url": "#18-performance-tuning", "type": "anchor", "status": "Anchor found: 1.8. Performance Tuning → #18-performance-tuning"}, {"text": "1.8.1. Queue Optimization", "url": "#181-queue-optimization", "type": "anchor", "status": "Anchor found: 1.8.1. Queue Optimization → #181-queue-optimization"}, {"text": "1.8.2. Memory Management", "url": "#182-memory-management", "type": "anchor", "status": "Anchor found: 1.8.2. Memory Management → #182-memory-management"}, {"text": "1.8.3. Scaling Strategies", "url": "#183-scaling-strategies", "type": "anchor", "status": "An<PERSON> found: 1.8.3. Scaling Strategies → #183-scaling-strategies"}, {"text": "1.9. Integration Strategies", "url": "#19-integration-strategies", "type": "anchor", "status": "<PERSON><PERSON> found: 1.9. Integration Strategies → #19-integration-strategies"}, {"text": "1.9.1. <PERSON><PERSON>", "url": "#191-laravel-pulse-integration", "type": "anchor", "status": "Anchor found: 1.9.1. <PERSON><PERSON> Pulse Integration → #191-laravel-pulse-integration"}, {"text": "1.9.2. Monitoring Stack", "url": "#192-monitoring-stack", "type": "anchor", "status": "Anchor found: 1.9.2. Monitoring Stack → #192-monitoring-stack"}, {"text": "1.9.3. Alerting Systems", "url": "#193-alerting-systems", "type": "anchor", "status": "Anchor found: 1.9.3. Alerting Systems → #193-alerting-systems"}, {"text": "1.10. Best Practices", "url": "#110-best-practices", "type": "anchor", "status": "Anchor found: 1.10. Best Practices → #110-best-practices"}, {"text": "1.10.1. Production Configuration", "url": "#1101-production-configuration", "type": "anchor", "status": "Anchor found: 1.10.1. Production Configuration → #1101-production-configuration"}, {"text": "1.10.2. Security Considerations", "url": "#1102-security-considerations", "type": "anchor", "status": "<PERSON><PERSON> found: 1.10.2. Security Considerations → #1102-security-considerations"}, {"text": "1.10.3. Maintenance Procedures", "url": "#1103-maintenance-procedures", "type": "anchor", "status": "Anchor found: 1.10.3. Maintenance Procedures → #1103-maintenance-procedures"}, {"text": "1.11. Troubleshooting", "url": "#111-troubleshooting", "type": "anchor", "status": "Anchor found: 1.11. Troubleshooting → #111-troubleshooting"}, {"text": "1.11.1. Common Issues", "url": "#1111-common-issues", "type": "anchor", "status": "Anchor found: 1.11.1. Common Issues → #1111-common-issues"}, {"text": "1.11.2. Debug Commands", "url": "#1112-debug-commands", "type": "anchor", "status": "Anchor found: 1.11.2. Debug Commands → #1112-debug-commands"}, {"text": "1.11.3. Performance Issues", "url": "#1113-performance-issues", "type": "anchor", "status": "Anchor found: 1.11.3. Performance Issues → #1113-performance-issues"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation → #navigation"}, {"text": "<PERSON><PERSON>ane with FrankenPHP Guide", "url": "040-laravel-octane-frankenphp-guide.md", "type": "internal", "status": "File exists: packages/040-laravel-octane-frankenphp-guide.md"}]}, {"file": "packages/060-laravel-data-guide.md", "total_links": 23, "internal_links": 2, "anchor_links": 21, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Installation & Setup", "url": "#2-installation--setup", "type": "anchor", "status": "Anchor found: 2. Installation & Setup → #2-installation--setup"}, {"text": "2.1. Package Installation", "url": "#21-package-installation", "type": "anchor", "status": "Anchor found: 2.1. Package Installation → #21-package-installation"}, {"text": "2.2. Configuration Publishing", "url": "#22-configuration-publishing", "type": "anchor", "status": "Anchor found: 2.2. Configuration Publishing → #22-configuration-publishing"}, {"text": "2.3. Basic Setup", "url": "#23-basic-setup", "type": "anchor", "status": "Anchor found: 2.3. Basic Setup → #23-basic-setup"}, {"text": "3. Data Transfer Objects", "url": "#3-data-transfer-objects", "type": "anchor", "status": "Anchor found: 3. Data Transfer Objects → #3-data-transfer-objects"}, {"text": "3.1. Basic DTO Creation", "url": "#31-basic-dto-creation", "type": "anchor", "status": "Anchor found: 3.1. Basic DTO Creation → #31-basic-dto-creation"}, {"text": "3.2. Advanced DTO Features", "url": "#32-advanced-dto-features", "type": "anchor", "status": "Anchor found: 3.2. Advanced DTO Features → #32-advanced-dto-features"}, {"text": "3.3. Nested DTOs", "url": "#33-nested-dtos", "type": "anchor", "status": "Anchor found: 3.3. Nested DTOs → #33-nested-dtos"}, {"text": "4. Validation & Transformation", "url": "#4-validation--transformation", "type": "anchor", "status": "An<PERSON> found: 4. Validation & Transformation → #4-validation--transformation"}, {"text": "4.1. Built-in Validation", "url": "#41-built-in-validation", "type": "anchor", "status": "Anchor found: 4.1. Built-in Validation → #41-built-in-validation"}, {"text": "4.2. Custom Validation Rules", "url": "#42-custom-validation-rules", "type": "anchor", "status": "Anchor found: 4.2. Custom Validation Rules → #42-custom-validation-rules"}, {"text": "4.3. Data Transformation", "url": "#43-data-transformation", "type": "anchor", "status": "<PERSON><PERSON> found: 4.3. Data Transformation → #43-data-transformation"}, {"text": "8. Taxonomy Integration", "url": "#8-taxonomy-integration", "type": "anchor", "status": "<PERSON><PERSON> found: 8. Taxonomy Integration → #8-taxonomy-integration"}, {"text": "8.1. Taxonomy Data Objects", "url": "#81-taxonomy-data-objects", "type": "anchor", "status": "<PERSON><PERSON> found: 8.1. Taxonomy Data Objects → #81-taxonomy-data-objects"}, {"text": "8.2. Chinook Taxonomy Examples", "url": "#82-chinook-taxonomy-examples", "type": "anchor", "status": "An<PERSON> found: 8.2. Chinook Taxonomy Examples → #82-chinook-taxonomy-examples"}, {"text": "8.3. Advanced Taxonomy Patterns", "url": "#83-advanced-taxonomy-patterns", "type": "anchor", "status": "Anchor found: 8.3. Advanced Taxonomy Patterns → #83-advanced-taxonomy-patterns"}, {"text": "9. Testing Strategies", "url": "#9-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 9. Testing Strategies → #9-testing-strategies"}, {"text": "9.1. Unit Testing Data Objects", "url": "#91-unit-testing-data-objects", "type": "anchor", "status": "An<PERSON> found: 9.1. Unit Testing Data Objects → #91-unit-testing-data-objects"}, {"text": "9.2. Integration Testing", "url": "#92-integration-testing", "type": "anchor", "status": "<PERSON><PERSON> found: 9.2. Integration Testing → #92-integration-testing"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation → #navigation"}, {"text": "Laravel Horizon Guide", "url": "050-laravel-horizon-guide.md", "type": "internal", "status": "File exists: packages/050-laravel-horizon-guide.md"}, {"text": "<PERSON><PERSON>", "url": "070-laravel-fractal-guide.md", "type": "internal", "status": "File exists: packages/070-laravel-fractal-guide.md"}]}, {"file": "packages/070-laravel-fractal-guide.md", "total_links": 15, "internal_links": 2, "anchor_links": 13, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Installation & Setup", "url": "#2-installation--setup", "type": "anchor", "status": "Anchor found: 2. Installation & Setup → #2-installation--setup"}, {"text": "2.1. Package Installation", "url": "#21-package-installation", "type": "anchor", "status": "Anchor found: 2.1. Package Installation → #21-package-installation"}, {"text": "2.2. Configuration Publishing", "url": "#22-configuration-publishing", "type": "anchor", "status": "Anchor found: 2.2. Configuration Publishing → #22-configuration-publishing"}, {"text": "2.3. Basic Setup", "url": "#23-basic-setup", "type": "anchor", "status": "Anchor found: 2.3. Basic Setup → #23-basic-setup"}, {"text": "3. Transformer Creation", "url": "#3-transformer-creation", "type": "anchor", "status": "Anchor found: 3. Transformer Creation → #3-transformer-creation"}, {"text": "3.1. Basic Transformers", "url": "#31-basic-transformers", "type": "anchor", "status": "An<PERSON> found: 3.1. Basic Transformers → #31-basic-transformers"}, {"text": "3.2. Advanced Transformers", "url": "#32-advanced-transformers", "type": "anchor", "status": "Anchor found: 3.2. Advanced Transformers → #32-advanced-transformers"}, {"text": "3.3. Nested Transformers", "url": "#33-nested-transformers", "type": "anchor", "status": "Anchor found: 3.3. Nested Transformers → #33-nested-transformers"}, {"text": "5. Taxonomy Integration", "url": "#5-taxonomy-integration", "type": "anchor", "status": "<PERSON><PERSON> found: 5. Taxonomy Integration → #5-taxonomy-integration"}, {"text": "5.1. Taxonomy Transformers", "url": "#51-taxonomy-transformers", "type": "anchor", "status": "<PERSON><PERSON> found: 5.1. Taxonomy Transformers → #51-taxonomy-transformers"}, {"text": "5.2. Hierarchical Data Handling", "url": "#52-hierarchical-data-handling", "type": "anchor", "status": "Anchor found: 5.2. Hierarchical Data Handling → #52-hierarchical-data-handling"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation → #navigation"}, {"text": "Laravel Data Guide", "url": "060-laravel-data-guide.md", "type": "internal", "status": "File exists: packages/060-laravel-data-guide.md"}, {"text": "Laravel Sanctum Guide", "url": "080-laravel-sanctum-guide.md", "type": "internal", "status": "File exists: packages/080-laravel-sanctum-guide.md"}]}, {"file": "packages/080-laravel-sanctum-guide.md", "total_links": 17, "internal_links": 2, "anchor_links": 15, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Installation & Setup", "url": "#2-installation--setup", "type": "anchor", "status": "Anchor found: 2. Installation & Setup → #2-installation--setup"}, {"text": "2.1. Package Installation", "url": "#21-package-installation", "type": "anchor", "status": "Anchor found: 2.1. Package Installation → #21-package-installation"}, {"text": "2.2. API Installation", "url": "#22-api-installation", "type": "anchor", "status": "Anchor found: 2.2. API Installation → #22-api-installation"}, {"text": "2.3. Configuration Setup", "url": "#23-configuration-setup", "type": "anchor", "status": "Anchor found: 2.3. Configuration Setup → #23-configuration-setup"}, {"text": "3. API Token Authentication", "url": "#3-api-token-authentication", "type": "anchor", "status": "Anchor found: 3. API Token Authentication → #3-api-token-authentication"}, {"text": "3.1. Token <PERSON>", "url": "#31-token-generation", "type": "anchor", "status": "Anchor found: 3.1. <PERSON><PERSON> → #31-token-generation"}, {"text": "3.2. Token Management", "url": "#32-token-management", "type": "anchor", "status": "Anchor found: 3.2. Token Management → #32-token-management"}, {"text": "3.3. Token Abilities", "url": "#33-token-abilities", "type": "anchor", "status": "An<PERSON> found: 3.3. <PERSON><PERSON> Abilities → #33-token-abilities"}, {"text": "5. Taxonomy-Aware Authentication", "url": "#5-taxonomy-aware-authentication", "type": "anchor", "status": "An<PERSON> found: 5. Taxonomy-Aware Authentication → #5-taxonomy-aware-authentication"}, {"text": "5.1. Genre-Based Permissions", "url": "#51-genre-based-permissions", "type": "anchor", "status": "Anchor found: 5.1. Genre-Based Permissions → #51-genre-based-permissions"}, {"text": "9. Testing Strategies", "url": "#9-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 9. Testing Strategies → #9-testing-strategies"}, {"text": "9.1. API Authentication Testing", "url": "#91-api-authentication-testing", "type": "anchor", "status": "Anchor found: 9.1. API Authentication Testing → #91-api-authentication-testing"}, {"text": "9.2. Token Abilities Testing", "url": "#92-token-abilities-testing", "type": "anchor", "status": "<PERSON><PERSON> found: 9.2. Token Abilities Testing → #92-token-abilities-testing"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation → #navigation"}, {"text": "<PERSON><PERSON>", "url": "070-laravel-fractal-guide.md", "type": "internal", "status": "File exists: packages/070-laravel-fractal-guide.md"}, {"text": "Laravel WorkOS Guide", "url": "090-laravel-workos-guide.md", "type": "internal", "status": "File exists: packages/090-laravel-workos-guide.md"}]}, {"file": "packages/090-laravel-workos-guide.md", "total_links": 18, "internal_links": 2, "anchor_links": 16, "external_links": 0, "broken_links": [{"text": "Package Index", "url": "../index.md", "type": "internal", "status": "File not found: index.md"}], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Installation & Configuration", "url": "#2-installation--configuration", "type": "anchor", "status": "Anchor found: 2. Installation & Configuration → #2-installation--configuration"}, {"text": "2.1. Package Installation", "url": "#21-package-installation", "type": "anchor", "status": "Anchor found: 2.1. Package Installation → #21-package-installation"}, {"text": "2.2. Environment Configuration", "url": "#22-environment-configuration", "type": "anchor", "status": "Anchor found: 2.2. Environment Configuration → #22-environment-configuration"}, {"text": "2.3. Service Provider Configuration", "url": "#23-service-provider-configuration", "type": "anchor", "status": "Anchor found: 2.3. Service Provider Configuration → #23-service-provider-configuration"}, {"text": "3. Authentication Setup", "url": "#3-authentication-setup", "type": "anchor", "status": "Anchor found: 3. Authentication Setup → #3-authentication-setup"}, {"text": "3.1. WorkOS Service Integration", "url": "#31-workos-service-integration", "type": "anchor", "status": "Anchor found: 3.1. WorkOS Service Integration → #31-workos-service-integration"}, {"text": "3.2. SSO Controller Implementation", "url": "#32-sso-controller-implementation", "type": "anchor", "status": "Anchor found: 3.2. SSO Controller Implementation → #32-sso-controller-implementation"}, {"text": "3.3. User Provisioning", "url": "#33-user-provisioning", "type": "anchor", "status": "Anchor found: 3.3. User Provisioning → #33-user-provisioning"}, {"text": "8. Testing Strategies", "url": "#8-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 8. Testing Strategies → #8-testing-strategies"}, {"text": "8.1. SSO Testing", "url": "#81-sso-testing", "type": "anchor", "status": "Anchor found: 8.1. SSO Testing → #81-sso-testing"}, {"text": "8.2. Directory Sync Testing", "url": "#82-directory-sync-testing", "type": "anchor", "status": "Anchor found: 8.2. Directory Sync Testing → #82-directory-sync-testing"}, {"text": "9. Monitoring & Troubleshooting", "url": "#9-monitoring--troubleshooting", "type": "anchor", "status": "Anchor found: 9. Monitoring & Troubleshooting → #9-monitoring--troubleshooting"}, {"text": "9.1. Health Checks", "url": "#91-health-checks", "type": "anchor", "status": "Anchor found: 9.1. Health Checks → #91-health-checks"}, {"text": "9.2. <PERSON><PERSON><PERSON>", "url": "#92-error-handling", "type": "anchor", "status": "Anchor found: 9.2. <PERSON><PERSON><PERSON> → #92-error-handling"}, {"text": "Navigation", "url": "#navigation", "type": "anchor", "status": "Anchor found: Navigation → #navigation"}, {"text": "Laravel Sanctum Guide", "url": "080-laravel-sanctum-guide.md", "type": "internal", "status": "File exists: packages/080-laravel-sanctum-guide.md"}]}, {"file": "packages/100-spatie-tags-guide.md", "total_links": 23, "internal_links": 12, "anchor_links": 11, "external_links": 0, "broken_links": [{"text": "Hierarchical Data Testing", "url": "../testing/080-hierarchical-data-testing.md", "type": "internal", "status": "File not found: testing/080-hierarchical-data-testing.md"}], "working_links": [{"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "1.2. <PERSON>gration Notice", "url": "#12-migration-notice", "type": "anchor", "status": "Anchor found: 1.2. <PERSON>gration Notice → #12-migration-notice"}, {"text": "1.2.1. Why This Change?", "url": "#121-why-this-change", "type": "anchor", "status": "Anchor found: 1.2.1. Why This Change? → #121-why-this-change"}, {"text": "1.2.2. Migration Path", "url": "#122-migration-path", "type": "anchor", "status": "Anchor found: 1.2.2. Migration Path → #122-migration-path"}, {"text": "1.3. New Taxonomy Architecture", "url": "#13-new-taxonomy-architecture", "type": "anchor", "status": "An<PERSON> found: 1.3. New Taxonomy Architecture → #13-new-taxonomy-architecture"}, {"text": "1.3.1. System Overview", "url": "#131-system-overview", "type": "anchor", "status": "Anchor found: 1.3.1. System Overview → #131-system-overview"}, {"text": "1.4. Greenfield Implementation Guide", "url": "#14-greenfield-implementation-guide", "type": "anchor", "status": "Anchor found: 1.4. Greenfield Implementation Guide → #14-greenfield-implementation-guide"}, {"text": "1.4.1. Installation", "url": "#141-installation", "type": "anchor", "status": "Anchor found: 1.4.1. Installation → #141-installation"}, {"text": "2. Complete Migration Example", "url": "#2-complete-migration-example", "type": "anchor", "status": "Anchor found: 2. Complete Migration Example → #2-complete-migration-example"}, {"text": "2.1. Before (spatie/laravel-tags)", "url": "#21-before-spat<PERSON><PERSON><PERSON>-tags", "type": "anchor", "status": "Anchor found: 2.1. Before (spatie/laravel-tags) → #21-before-spatielaravel-tags"}, {"text": "3. Migration Support", "url": "#3-migration-support", "type": "anchor", "status": "Anchor found: 3. Migration Support → #3-migration-support"}, {"text": "3.1. Documentation Resources", "url": "#31-documentation-resources", "type": "anchor", "status": "Anchor found: 3.1. Documentation Resources → #31-documentation-resources"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Chinook Seeders Guide", "url": "../040-chinook-seeders-guide.md", "type": "internal", "status": "File exists: 040-chinook-seeders-guide.md"}, {"text": "Performance Optimization Guide", "url": "../performance/000-performance-index.md", "type": "internal", "status": "File exists: performance/000-performance-index.md"}, {"text": "taxonomy guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "complete taxonomy guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Laravel WorkOS Guide", "url": "090-laravel-workos-guide.md", "type": "internal", "status": "File exists: packages/090-laravel-workos-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Packages Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}]}, {"file": "packages/110-aliziodev-laravel-taxonomy-guide.md", "total_links": 38, "internal_links": 8, "anchor_links": 29, "external_links": 1, "broken_links": [{"text": "Hierarchical Data Testing", "url": "../testing/080-hierarchical-data-testing.md", "type": "internal", "status": "File not found: testing/080-hierarchical-data-testing.md"}], "working_links": [{"text": "1.3. Overview", "url": "#13-overview", "type": "anchor", "status": "Anchor found: 1.3. <PERSON><PERSON> → #13-overview"}, {"text": "1.3.1. Key Features", "url": "#131-key-features", "type": "anchor", "status": "Anchor found: 1.3.1. Key Features → #131-key-features"}, {"text": "2. Installation & Configuration", "url": "#2-installation--configuration", "type": "anchor", "status": "Anchor found: 2. Installation & Configuration → #2-installation--configuration"}, {"text": "2.1. Package Installation", "url": "#21-package-installation", "type": "anchor", "status": "Anchor found: 2.1. Package Installation → #21-package-installation"}, {"text": "2.2. Configuration Publishing", "url": "#22-configuration-publishing", "type": "anchor", "status": "Anchor found: 2.2. Configuration Publishing → #22-configuration-publishing"}, {"text": "2.3. Migration Execution", "url": "#23-migration-execution", "type": "anchor", "status": "Anchor found: 2.3. Migration Execution → #23-migration-execution"}, {"text": "2.4. Configuration Customization", "url": "#24-configuration-customization", "type": "anchor", "status": "Anchor found: 2.4. Configuration Customization → #24-configuration-customization"}, {"text": "3. Single Taxonomy Architecture", "url": "#3-single-taxonomy-architecture", "type": "anchor", "status": "Anchor found: 3. Single Taxonomy Architecture → #3-single-taxonomy-architecture"}, {"text": "3.1. Clean System Design", "url": "#31-clean-system-design", "type": "anchor", "status": "Anchor found: 3.1. Clean System Design → #31-clean-system-design"}, {"text": "3.2. Direct Taxonomy Mapping Implementation", "url": "#32-direct-taxonomy-mapping-implementation", "type": "anchor", "status": "An<PERSON> found: 3.2. Direct Taxonomy Mapping Implementation → #32-direct-taxonomy-mapping-implementation"}, {"text": "4. <PERSON><PERSON> 12 Modern Implementation", "url": "#4-laravel-12-modern-implementation", "type": "anchor", "status": "Anchor found: 4. <PERSON><PERSON> 12 Modern Implementation → #4-laravel-12-modern-implementation"}, {"text": "4.1. Model Integration Patterns", "url": "#41-model-integration-patterns", "type": "anchor", "status": "<PERSON><PERSON> found: 4.1. Model Integration Patterns → #41-model-integration-patterns"}, {"text": "4.2. Modern Casting Syntax", "url": "#42-modern-casting-syntax", "type": "anchor", "status": "Anchor found: 4.2. Modern Casting Syntax → #42-modern-casting-syntax"}, {"text": "5. Chinook Integration Patterns", "url": "#5-chinook-integration-patterns", "type": "anchor", "status": "Anchor found: 5. Chinook Integration Patterns → #5-chinook-integration-patterns"}, {"text": "5.1. Track Model Implementation", "url": "#51-track-model-implementation", "type": "anchor", "status": "Anchor found: 5.1. Track Model Implementation → #51-track-model-implementation"}, {"text": "5.2. Artist Model Implementation", "url": "#52-artist-model-implementation", "type": "anchor", "status": "Anchor found: 5.2. Artist Model Implementation → #52-artist-model-implementation"}, {"text": "6. Advanced Usage Examples", "url": "#6-advanced-usage-examples", "type": "anchor", "status": "Anchor found: 6. Advanced Usage Examples → #6-advanced-usage-examples"}, {"text": "6.1. Polymorphic Relationships", "url": "#61-polymorphic-relationships", "type": "anchor", "status": "An<PERSON> found: 6.1. Polymorphic Relationships → #61-polymorphic-relationships"}, {"text": "6.2. Hierarchical Taxonomies", "url": "#62-hierarchical-taxonomies", "type": "anchor", "status": "Anchor found: 6.2. Hierarchical Taxonomies → #62-hierarchical-taxonomies"}, {"text": "7. Testing Integration", "url": "#7-testing-integration", "type": "anchor", "status": "An<PERSON> found: 7. Testing Integration → #7-testing-integration"}, {"text": "7.1. Pest Framework Examples", "url": "#71-pest-framework-examples", "type": "anchor", "status": "An<PERSON> found: 7.1. Pest Framework Examples → #71-pest-framework-examples"}, {"text": "8. Performance Optimization", "url": "#8-performance-optimization", "type": "anchor", "status": "Anchor found: 8. Performance Optimization → #8-performance-optimization"}, {"text": "8.1. Query Optimization", "url": "#81-query-optimization", "type": "anchor", "status": "Anchor found: 8.1. Query Optimization → #81-query-optimization"}, {"text": "8.2. Caching Strategies", "url": "#82-caching-strategies", "type": "anchor", "status": "Anchor found: 8.2. Caching Strategies → #82-caching-strategies"}, {"text": "9. Genre Preservation Strategy", "url": "#9-genre-preservation-strategy", "type": "anchor", "status": "Anchor found: 9. Genre Preservation Strategy → #9-genre-preservation-strategy"}, {"text": "9.1. Compatibility Layer", "url": "#91-compatibility-layer", "type": "anchor", "status": "Anchor found: 9.1. Compatibility Layer → #91-compatibility-layer"}, {"text": "9.2. <PERSON><PERSON>", "url": "#92-migration-patterns", "type": "anchor", "status": "Anchor found: 9.2. Migration Patterns → #92-migration-patterns"}, {"text": "aliziodev/laravel-taxonomy GitHub Repository", "url": "https://github.com/aliziodev/laravel-taxonomy", "type": "external", "status": "External link (not validated)"}, {"text": "Chinook Main Index", "url": "../000-chinook-index.md", "type": "internal", "status": "File exists: 000-chinook-index.md"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Chinook Migrations Guide", "url": "../020-chinook-migrations-guide.md", "type": "internal", "status": "File exists: 020-chinook-migrations-guide.md"}, {"text": "⚠️ DEPRECATED: <PERSON><PERSON>s Guide", "url": "./100-spatie-tags-guide.md", "type": "internal", "status": "File exists: packages/100-spatie-tags-guide.md"}, {"text": "Spatie Permission Guide", "url": "./140-spatie-permission-guide.md", "type": "internal", "status": "File exists: packages/140-spatie-permission-guide.md"}, {"text": "Packages Index", "url": "./000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}, {"text": "Performance Optimization Guide", "url": "../performance/000-performance-index.md", "type": "internal", "status": "File exists: performance/000-performance-index.md"}, {"text": "9. Genre Preservation Strategy", "url": "#9-genre-preservation-strategy", "type": "anchor", "status": "Anchor found: 9. Genre Preservation Strategy → #9-genre-preservation-strategy"}, {"text": "Table of Contents", "url": "#12-table-of-contents", "type": "anchor", "status": "Anchor found: 1.2. Table of Contents → #12-table-of-contents"}]}, {"file": "packages/120-spatie-media-library-guide.md", "total_links": 13, "internal_links": 2, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Installation & Configuration", "url": "#13-installation--configuration", "type": "anchor", "status": "Anchor found: 1.3 Installation & Configuration → #13-installation--configuration"}, {"text": "1.4 Basic Media Implementation", "url": "#14-basic-media-implementation", "type": "anchor", "status": "Anchor found: 1.4 Basic Media Implementation → #14-basic-media-implementation"}, {"text": "1.5 Advanced Media Patterns", "url": "#15-advanced-media-patterns", "type": "anchor", "status": "Anchor found: 1.5 Advanced Media Patterns → #15-advanced-media-patterns"}, {"text": "1.6 Chinook Integration", "url": "#16-chinook-integration", "type": "anchor", "status": "Anchor found: 1.6 Chinook Integration → #16-chinook-integration"}, {"text": "1.7 File Conversions & Processing", "url": "#17-file-conversions--processing", "type": "anchor", "status": "Anchor found: 1.7 File Conversions & Processing → #17-file-conversions--processing"}, {"text": "1.8 Performance Optimization", "url": "#18-performance-optimization", "type": "anchor", "status": "Anchor found: 1.8 Performance Optimization → #18-performance-optimization"}, {"text": "1.9 CDN Integration", "url": "#19-cdn-integration", "type": "anchor", "status": "Anchor found: 1.9 CDN Integration → #19-cdn-integration"}, {"text": "1.10 Testing Strategies", "url": "#110-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 1.10 Testing Strategies → #110-testing-strategies"}, {"text": "1.11 Production Deployment", "url": "#111-production-deployment", "type": "anchor", "status": "Anchor found: 1.11 Production Deployment → #111-production-deployment"}, {"text": "1.12 Best Practices", "url": "#112-best-practices", "type": "anchor", "status": "Anchor found: 1.12 Best Practices → #112-best-practices"}, {"text": "Spatie Permission Guide", "url": "140-spatie-permission-guide.md", "type": "internal", "status": "File exists: packages/140-spatie-permission-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}]}, {"file": "packages/140-spatie-permission-guide.md", "total_links": 13, "internal_links": 2, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Installation & Configuration", "url": "#13-installation--configuration", "type": "anchor", "status": "Anchor found: 1.3 Installation & Configuration → #13-installation--configuration"}, {"text": "1.4 Basic RBAC Implementation", "url": "#14-basic-rbac-implementation", "type": "anchor", "status": "Anchor found: 1.4 Basic RBAC Implementation → #14-basic-rbac-implementation"}, {"text": "1.5 Advanced Permission Patterns", "url": "#15-advanced-permission-patterns", "type": "anchor", "status": "Anchor found: 1.5 Advanced Permission Patterns → #15-advanced-permission-patterns"}, {"text": "1.6 Chinook Integration", "url": "#16-chinook-integration", "type": "anchor", "status": "Anchor found: 1.6 Chinook Integration → #16-chinook-integration"}, {"text": "1.7 Hierarchical Roles", "url": "#17-hierarchical-roles", "type": "anchor", "status": "Anchor found: 1.7 Hierarchical Roles → #17-hierarchical-roles"}, {"text": "1.8 Performance Optimization", "url": "#18-performance-optimization", "type": "anchor", "status": "Anchor found: 1.8 Performance Optimization → #18-performance-optimization"}, {"text": "1.9 API Integration", "url": "#19-api-integration", "type": "anchor", "status": "Anchor found: 1.9 API Integration → #19-api-integration"}, {"text": "1.10 Testing Strategies", "url": "#110-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 1.10 Testing Strategies → #110-testing-strategies"}, {"text": "1.11 Production Deployment", "url": "#111-production-deployment", "type": "anchor", "status": "Anchor found: 1.11 Production Deployment → #111-production-deployment"}, {"text": "1.12 Best Practices", "url": "#112-best-practices", "type": "anchor", "status": "Anchor found: 1.12 Best Practices → #112-best-practices"}, {"text": "Spatie Comments Guide", "url": "150-spatie-comments-guide.md", "type": "internal", "status": "File exists: packages/150-spatie-comments-guide.md"}, {"text": "Spatie Media Library Guide", "url": "120-spatie-media-library-guide.md", "type": "internal", "status": "File exists: packages/120-spatie-media-library-guide.md"}]}, {"file": "packages/150-spatie-comments-guide.md", "total_links": 12, "internal_links": 2, "anchor_links": 10, "external_links": 0, "broken_links": [{"text": "<PERSON><PERSON> Guide", "url": "160-spatie-backup-guide.md", "type": "internal", "status": "File not found: packages/160-spatie-backup-guide.md"}], "working_links": [{"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Installation & Configuration", "url": "#13-installation--configuration", "type": "anchor", "status": "Anchor found: 1.3 Installation & Configuration → #13-installation--configuration"}, {"text": "1.4 Comment System Architecture", "url": "#14-comment-system-architecture", "type": "anchor", "status": "Anchor found: 1.4 Comment System Architecture → #14-comment-system-architecture"}, {"text": "1.5 Livewire Integration", "url": "#15-livewire-integration", "type": "anchor", "status": "Anchor found: 1.5 Livewire Integration → #15-livewire-integration"}, {"text": "1.6 Moderation Workflows", "url": "#16-moderation-workflows", "type": "anchor", "status": "Anchor found: 1.6 Moderation Workflows → #16-moderation-workflows"}, {"text": "1.7 Notification Systems", "url": "#17-notification-systems", "type": "anchor", "status": "An<PERSON> found: 1.7 Notification Systems → #17-notification-systems"}, {"text": "1.8 Performance Optimization", "url": "#18-performance-optimization", "type": "anchor", "status": "Anchor found: 1.8 Performance Optimization → #18-performance-optimization"}, {"text": "1.9 Testing Strategies", "url": "#19-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 1.9 Testing Strategies → #19-testing-strategies"}, {"text": "1.10 Production Deployment", "url": "#110-production-deployment", "type": "anchor", "status": "Anchor found: 1.10 Production Deployment → #110-production-deployment"}, {"text": "1.11 Best Practices", "url": "#111-best-practices", "type": "anchor", "status": "Anchor found: 1.11 Best Practices → #111-best-practices"}, {"text": "Spatie Permission Guide", "url": "140-spatie-permission-guide.md", "type": "internal", "status": "File exists: packages/140-spatie-permission-guide.md"}]}, {"file": "packages/160-spatie-activitylog-guide.md", "total_links": 18, "internal_links": 6, "anchor_links": 12, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Installation & Configuration", "url": "#2-installation--configuration", "type": "anchor", "status": "Anchor found: 2. Installation & Configuration → #2-installation--configuration"}, {"text": "3. Basic Activity Logging", "url": "#3-basic-activity-logging", "type": "anchor", "status": "Anchor found: 3. Basic Activity Logging → #3-basic-activity-logging"}, {"text": "4. Advanced Logging Patterns", "url": "#4-advanced-logging-patterns", "type": "anchor", "status": "Anchor found: 4. Advanced Logging Patterns → #4-advanced-logging-patterns"}, {"text": "5. Chinook Integration", "url": "#5-chinook-integration", "type": "anchor", "status": "Anchor found: 5. Chinook Integration → #5-chinook-integration"}, {"text": "6. Custom Activity Models", "url": "#6-custom-activity-models", "type": "anchor", "status": "Anchor found: 6. Custom Activity Models → #6-custom-activity-models"}, {"text": "7. Performance Optimization", "url": "#7-performance-optimization", "type": "anchor", "status": "Anchor found: 7. Performance Optimization → #7-performance-optimization"}, {"text": "8. Security & Compliance", "url": "#8-security--compliance", "type": "anchor", "status": "Anchor found: 8. Security & Compliance → #8-security--compliance"}, {"text": "9. Real-time Activity Monitoring", "url": "#9-real-time-activity-monitoring", "type": "anchor", "status": "Anchor found: 9. Real-time Activity Monitoring → #9-real-time-activity-monitoring"}, {"text": "10. Testing Strategies", "url": "#10-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 10. Testing Strategies → #10-testing-strategies"}, {"text": "11. Production Deployment", "url": "#11-production-deployment", "type": "anchor", "status": "Anchor found: 11. Production Deployment → #11-production-deployment"}, {"text": "12. Best Practices", "url": "#12-best-practices", "type": "anchor", "status": "Anchor found: 12. Best Practices → #12-best-practices"}, {"text": "Previous: <PERSON><PERSON> Guide", "url": "150-spatie-comments-guide.md", "type": "internal", "status": "File exists: packages/150-spatie-comments-guide.md"}, {"text": "<PERSON><PERSON> Settings Guide", "url": "180-spatie-laravel-settings-guide.md", "type": "internal", "status": "File exists: packages/180-spatie-laravel-settings-guide.md"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Package Testing Guide", "url": "testing/010-pest-testing-guide.md", "type": "internal", "status": "File exists: packages/testing/010-pest-testing-guide.md"}, {"text": "Packages Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}]}, {"file": "packages/170-laravel-folio-guide.md", "total_links": 19, "internal_links": 7, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Livewire/Volt Guide", "url": "../livewire/010-volt-functional-components.md", "type": "internal", "status": "File not found: livewire/010-volt-functional-components.md"}, {"text": "SEO Optimization Guide", "url": "../seo/010-taxonomy-seo-guide.md", "type": "internal", "status": "File not found: seo/010-taxonomy-seo-guide.md"}], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Installation & Configuration", "url": "#2-installation--configuration", "type": "anchor", "status": "Anchor found: 2. Installation & Configuration → #2-installation--configuration"}, {"text": "3. Taxonomy-Aware Page Routing", "url": "#3-taxonomy-aware-page-routing", "type": "anchor", "status": "Anchor found: 3. Taxonomy-Aware Page Routing → #3-taxonomy-aware-page-routing"}, {"text": "4. Livewire/Volt Integration", "url": "#4-livewirevolt-integration", "type": "anchor", "status": "Anchor found: 4. Livewire/Volt Integration → #4-livewirevolt-integration"}, {"text": "5. Genre-Based Route Model Binding", "url": "#5-genre-based-route-model-binding", "type": "anchor", "status": "Anchor found: 5. Genre-Based Route Model Binding → #5-genre-based-route-model-binding"}, {"text": "6. Middleware Integration", "url": "#6-middleware-integration", "type": "anchor", "status": "Anchor found: 6. Middleware Integration → #6-middleware-integration"}, {"text": "7. SEO Optimization with Taxonomies", "url": "#7-seo-optimization-with-taxonomies", "type": "anchor", "status": "An<PERSON> found: 7. SEO Optimization with Taxonomies → #7-seo-optimization-with-taxonomies"}, {"text": "8. Performance Considerations", "url": "#8-performance-considerations", "type": "anchor", "status": "Anchor found: 8. Performance Considerations → #8-performance-considerations"}, {"text": "9. Testing Strategies", "url": "#9-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 9. Testing Strategies → #9-testing-strategies"}, {"text": "10. Production Deployment", "url": "#10-production-deployment", "type": "anchor", "status": "Anchor found: 10. Production Deployment → #10-production-deployment"}, {"text": "11. Best Practices", "url": "#11-best-practices", "type": "anchor", "status": "Anchor found: 11. Best Practices → #11-best-practices"}, {"text": "12. Real-World Examples", "url": "#12-real-world-examples", "type": "anchor", "status": "Anchor found: 12. Real-World Examples → #12-real-world-examples"}, {"text": "Previous: <PERSON><PERSON> Translatable Guide", "url": "220-spatie-laravel-translatable-guide.md", "type": "internal", "status": "File exists: packages/220-spatie-laravel-translatable-guide.md"}, {"text": "Nnjeim World Guide", "url": "190-nn<PERSON><PERSON>-world-guide.md", "type": "internal", "status": "File exists: packages/190-nnjeim-world-guide.md"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Packages Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}]}, {"file": "packages/180-spatie-laravel-settings-guide.md", "total_links": 19, "internal_links": 7, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Filament Settings Pages", "url": "../filament/pages/000-pages-index.md", "type": "internal", "status": "File not found: filament/pages/000-pages-index.md"}], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Installation & Configuration", "url": "#2-installation--configuration", "type": "anchor", "status": "Anchor found: 2. Installation & Configuration → #2-installation--configuration"}, {"text": "3. <PERSON><PERSON> 12 Modern Implementation", "url": "#3-laravel-12-modern-implementation", "type": "anchor", "status": "Anchor found: 3. <PERSON><PERSON> 12 Modern Implementation → #3-laravel-12-modern-implementation"}, {"text": "4. Taxonomy-Enhanced Settings Architecture", "url": "#4-taxonomy-enhanced-settings-architecture", "type": "anchor", "status": "Anchor found: 4. Taxonomy-Enhanced Settings Architecture → #4-taxonomy-enhanced-settings-architecture"}, {"text": "5. <PERSON><PERSON> Settings Integration", "url": "#5-chinook-settings-integration", "type": "anchor", "status": "Anchor found: 5. <PERSON><PERSON> Settings Integration → #5-chinook-settings-integration"}, {"text": "6. <PERSON><PERSON><PERSON> Admin <PERSON>", "url": "#6-filament-admin-integration", "type": "anchor", "status": "Anchor found: 6. Filament Admin Integration → #6-filament-admin-integration"}, {"text": "7. Advanced Features", "url": "#7-advanced-features", "type": "anchor", "status": "Anchor found: 7. Advanced Features → #7-advanced-features"}, {"text": "8. Testing Strategies", "url": "#8-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 8. Testing Strategies → #8-testing-strategies"}, {"text": "9. Performance Optimization", "url": "#9-performance-optimization", "type": "anchor", "status": "Anchor found: 9. Performance Optimization → #9-performance-optimization"}, {"text": "10. Security & Compliance", "url": "#10-security--compliance", "type": "anchor", "status": "Anchor found: 10. Security & Compliance → #10-security--compliance"}, {"text": "11. Production Deployment", "url": "#11-production-deployment", "type": "anchor", "status": "Anchor found: 11. Production Deployment → #11-production-deployment"}, {"text": "12. Best Practices", "url": "#12-best-practices", "type": "anchor", "status": "Anchor found: 12. Best Practices → #12-best-practices"}, {"text": "Previous: Spatie Activity Log Guide", "url": "160-spatie-activitylog-guide.md", "type": "internal", "status": "File exists: packages/160-spatie-activitylog-guide.md"}, {"text": "<PERSON><PERSON> Query Builder Guide", "url": "200-spatie-laravel-query-builder-guide.md", "type": "internal", "status": "File exists: packages/200-spatie-laravel-query-builder-guide.md"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Package Testing Guide", "url": "testing/010-pest-testing-guide.md", "type": "internal", "status": "File exists: packages/testing/010-pest-testing-guide.md"}, {"text": "Packages Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}]}, {"file": "packages/190-nnjeim-world-guide.md", "total_links": 19, "internal_links": 7, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "API Testing Guide", "url": "../testing/020-api-testing-guide.md", "type": "internal", "status": "File not found: testing/020-api-testing-guide.md"}, {"text": "Performance Optimization Guide", "url": "../performance/010-database-optimization.md", "type": "internal", "status": "File not found: performance/010-database-optimization.md"}], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Installation & Configuration", "url": "#2-installation--configuration", "type": "anchor", "status": "Anchor found: 2. Installation & Configuration → #2-installation--configuration"}, {"text": "3. Geographic Taxonomy Integration", "url": "#3-geographic-taxonomy-integration", "type": "anchor", "status": "An<PERSON> found: 3. Geographic Taxonomy Integration → #3-geographic-taxonomy-integration"}, {"text": "4. Music Origin & Regional Data", "url": "#4-music-origin--regional-data", "type": "anchor", "status": "Anchor found: 4. Music Origin & Regional Data → #4-music-origin--regional-data"}, {"text": "5. API Integration Patterns", "url": "#5-api-integration-patterns", "type": "anchor", "status": "Anchor found: 5. API Integration Patterns → #5-api-integration-patterns"}, {"text": "6. Frontend Components", "url": "#6-frontend-components", "type": "anchor", "status": "Anchor found: 6. Frontend Components → #6-frontend-components"}, {"text": "7. Performance Optimization", "url": "#7-performance-optimization", "type": "anchor", "status": "Anchor found: 7. Performance Optimization → #7-performance-optimization"}, {"text": "8. User Profile Integration", "url": "#8-user-profile-integration", "type": "anchor", "status": "Anchor found: 8. User Profile Integration → #8-user-profile-integration"}, {"text": "9. Business Logic Integration", "url": "#9-business-logic-integration", "type": "anchor", "status": "An<PERSON> found: 9. Business Logic Integration → #9-business-logic-integration"}, {"text": "10. Testing Strategies", "url": "#10-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 10. Testing Strategies → #10-testing-strategies"}, {"text": "11. Production Deployment", "url": "#11-production-deployment", "type": "anchor", "status": "Anchor found: 11. Production Deployment → #11-production-deployment"}, {"text": "12. Best Practices", "url": "#12-best-practices", "type": "anchor", "status": "Anchor found: 12. Best Practices → #12-best-practices"}, {"text": "Previous: <PERSON>vel Folio Guide", "url": "170-laravel-folio-guide.md", "type": "internal", "status": "File exists: packages/170-laravel-folio-guide.md"}, {"text": "Laravel Optimize Database Guide", "url": "210-laravel-optimize-database-guide.md", "type": "internal", "status": "File exists: packages/210-laravel-optimize-database-guide.md"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Packages Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}]}, {"file": "packages/200-spatie-laravel-query-builder-guide.md", "total_links": 19, "internal_links": 7, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "API Testing Guide", "url": "../testing/020-api-testing-guide.md", "type": "internal", "status": "File not found: testing/020-api-testing-guide.md"}], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Installation & Configuration", "url": "#2-installation--configuration", "type": "anchor", "status": "Anchor found: 2. Installation & Configuration → #2-installation--configuration"}, {"text": "3. <PERSON><PERSON> 12 Modern Implementation", "url": "#3-laravel-12-modern-implementation", "type": "anchor", "status": "Anchor found: 3. <PERSON><PERSON> 12 Modern Implementation → #3-laravel-12-modern-implementation"}, {"text": "4. Taxonomy-Enhanced Query Building", "url": "#4-taxonomy-enhanced-query-building", "type": "anchor", "status": "Anchor found: 4. Taxonomy-Enhanced Query Building → #4-taxonomy-enhanced-query-building"}, {"text": "5. Chinook API Endpoints", "url": "#5-chinook-api-endpoints", "type": "anchor", "status": "Anchor found: 5. Chinook API Endpoints → #5-chinook-api-endpoints"}, {"text": "6. Advanced Filtering with Taxonomies", "url": "#6-advanced-filtering-with-taxonomies", "type": "anchor", "status": "Anchor found: 6. Advanced Filtering with Taxonomies → #6-advanced-filtering-with-taxonomies"}, {"text": "7. Security & Validation", "url": "#7-security--validation", "type": "anchor", "status": "Anchor found: 7. Security & Validation → #7-security--validation"}, {"text": "8. Testing Strategies", "url": "#8-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 8. Testing Strategies → #8-testing-strategies"}, {"text": "9. Performance Optimization", "url": "#9-performance-optimization", "type": "anchor", "status": "Anchor found: 9. Performance Optimization → #9-performance-optimization"}, {"text": "10. Real-time Query Analytics", "url": "#10-real-time-query-analytics", "type": "anchor", "status": "Anchor found: 10. Real-time Query Analytics → #10-real-time-query-analytics"}, {"text": "11. Production Deployment", "url": "#11-production-deployment", "type": "anchor", "status": "Anchor found: 11. Production Deployment → #11-production-deployment"}, {"text": "12. Best Practices", "url": "#12-best-practices", "type": "anchor", "status": "Anchor found: 12. Best Practices → #12-best-practices"}, {"text": "Previous: <PERSON><PERSON> Guide", "url": "180-spatie-laravel-settings-guide.md", "type": "internal", "status": "File exists: packages/180-spatie-laravel-settings-guide.md"}, {"text": "<PERSON><PERSON> Translatable Guide", "url": "220-spatie-laravel-translatable-guide.md", "type": "internal", "status": "File exists: packages/220-spatie-laravel-translatable-guide.md"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Package Testing Guide", "url": "testing/010-pest-testing-guide.md", "type": "internal", "status": "File exists: packages/testing/010-pest-testing-guide.md"}, {"text": "Packages Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}]}, {"file": "packages/210-laravel-optimize-database-guide.md", "total_links": 19, "internal_links": 7, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Package Integration Patterns", "url": "../integration-patterns.md", "type": "internal", "status": "File not found: integration-patterns.md"}, {"text": "Performance Testing Guide", "url": "../testing/030-performance-testing.md", "type": "internal", "status": "File not found: testing/030-performance-testing.md"}, {"text": "Production Deployment Guide", "url": "../deployment/010-production-deployment.md", "type": "internal", "status": "File not found: deployment/010-production-deployment.md"}], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Installation & Configuration", "url": "#2-installation--configuration", "type": "anchor", "status": "Anchor found: 2. Installation & Configuration → #2-installation--configuration"}, {"text": "3. Taxonomy-Optimized Database Strategies", "url": "#3-taxonomy-optimized-database-strategies", "type": "anchor", "status": "Anchor found: 3. Taxonomy-Optimized Database Strategies → #3-taxonomy-optimized-database-strategies"}, {"text": "4. Index Optimization for Taxonomies", "url": "#4-index-optimization-for-taxonomies", "type": "anchor", "status": "<PERSON><PERSON> found: 4. Index Optimization for Taxonomies → #4-index-optimization-for-taxonomies"}, {"text": "5. <PERSON><PERSON>", "url": "#5-laravel-pulse-integration", "type": "anchor", "status": "Anchor found: 5. <PERSON><PERSON>ulse Integration → #5-laravel-pulse-integration"}, {"text": "6. Automated Optimization Workflows", "url": "#6-automated-optimization-workflows", "type": "anchor", "status": "Anchor found: 6. Automated Optimization Workflows → #6-automated-optimization-workflows"}, {"text": "7. Query Performance Analysis", "url": "#7-query-performance-analysis", "type": "anchor", "status": "Anchor found: 7. Query Performance Analysis → #7-query-performance-analysis"}, {"text": "8. Taxonomy-Specific Optimizations", "url": "#8-taxonomy-specific-optimizations", "type": "anchor", "status": "An<PERSON> found: 8. Taxonomy-Specific Optimizations → #8-taxonomy-specific-optimizations"}, {"text": "9. Monitoring & Alerting", "url": "#9-monitoring--alerting", "type": "anchor", "status": "Anchor found: 9. Monitoring & Alerting → #9-monitoring--alerting"}, {"text": "10. Testing Strategies", "url": "#10-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 10. Testing Strategies → #10-testing-strategies"}, {"text": "11. Production Deployment", "url": "#11-production-deployment", "type": "anchor", "status": "Anchor found: 11. Production Deployment → #11-production-deployment"}, {"text": "12. Best Practices", "url": "#12-best-practices", "type": "anchor", "status": "Anchor found: 12. Best Practices → #12-best-practices"}, {"text": "Previous: NNJeim World Guide", "url": "190-nn<PERSON><PERSON>-world-guide.md", "type": "internal", "status": "File exists: packages/190-nnjeim-world-guide.md"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Packages Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}]}, {"file": "packages/220-spatie-laravel-translatable-guide.md", "total_links": 19, "internal_links": 7, "anchor_links": 12, "external_links": 0, "broken_links": [{"text": "Package Integration Patterns", "url": "../integration-patterns.md", "type": "internal", "status": "File not found: integration-patterns.md"}, {"text": "API Testing Guide", "url": "../testing/020-api-testing-guide.md", "type": "internal", "status": "File not found: testing/020-api-testing-guide.md"}], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Installation & Configuration", "url": "#2-installation--configuration", "type": "anchor", "status": "Anchor found: 2. Installation & Configuration → #2-installation--configuration"}, {"text": "3. <PERSON><PERSON> 12 Modern Implementation", "url": "#3-laravel-12-modern-implementation", "type": "anchor", "status": "Anchor found: 3. <PERSON><PERSON> 12 Modern Implementation → #3-laravel-12-modern-implementation"}, {"text": "4. Multilingual Taxonomy Integration", "url": "#4-multilingual-taxonomy-integration", "type": "anchor", "status": "<PERSON><PERSON> found: 4. Multilingual Taxonomy Integration → #4-multilingual-taxonomy-integration"}, {"text": "5. Chinook Model Translations", "url": "#5-chinook-model-translations", "type": "anchor", "status": "Anchor found: 5. Chinook Model Translations → #5-chinook-model-translations"}, {"text": "6. Advanced Translation Features", "url": "#6-advanced-translation-features", "type": "anchor", "status": "Anchor found: 6. Advanced Translation Features → #6-advanced-translation-features"}, {"text": "7. <PERSON><PERSON><PERSON> Admin <PERSON>", "url": "#7-filament-admin-integration", "type": "anchor", "status": "Anchor found: 7. Filament Admin Integration → #7-filament-admin-integration"}, {"text": "8. Testing Strategies", "url": "#8-testing-strategies", "type": "anchor", "status": "An<PERSON> found: 8. Testing Strategies → #8-testing-strategies"}, {"text": "9. Performance Optimization", "url": "#9-performance-optimization", "type": "anchor", "status": "Anchor found: 9. Performance Optimization → #9-performance-optimization"}, {"text": "10. API Internationalization", "url": "#10-api-internationalization", "type": "anchor", "status": "An<PERSON> found: 10. API Internationalization → #10-api-internationalization"}, {"text": "11. Production Deployment", "url": "#11-production-deployment", "type": "anchor", "status": "Anchor found: 11. Production Deployment → #11-production-deployment"}, {"text": "12. Best Practices", "url": "#12-best-practices", "type": "anchor", "status": "Anchor found: 12. Best Practices → #12-best-practices"}, {"text": "Previous: <PERSON><PERSON>ry Builder Guide", "url": "200-spatie-laravel-query-builder-guide.md", "type": "internal", "status": "File exists: packages/200-spatie-laravel-query-builder-guide.md"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Aliziodev Laravel Taxonomy Guide", "url": "110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Filament Internationalization", "url": "../filament/internationalization/000-internationalization-index.md", "type": "internal", "status": "File exists: filament/internationalization/000-internationalization-index.md"}, {"text": "Packages Index", "url": "000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}]}, {"file": "packages/development/000-development-index.md", "total_links": 15, "internal_links": 0, "anchor_links": 15, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Development Tools Index", "url": "#1-development-tools-index", "type": "anchor", "status": "Anchor found: 1. Development Tools Index → #1-development-tools-index"}, {"text": "1.1 Overview", "url": "#11-overview", "type": "anchor", "status": "Anchor found: 1.1 Overview → #11-overview"}, {"text": "1.1.1 Tool Categories", "url": "#111-tool-categories", "type": "anchor", "status": "Anchor found: 1.1.1 Tool Categories → #111-tool-categories"}, {"text": "1.1.2 Architecture Overview", "url": "#112-architecture-overview", "type": "anchor", "status": "Anchor found: 1.1.2 Architecture Overview → #112-architecture-overview"}, {"text": "1.1.3 Taxonomy Integration Benefits", "url": "#113-taxonomy-integration-benefits", "type": "anchor", "status": "<PERSON><PERSON> found: 1.1.3 Taxonomy Integration Benefits → #113-taxonomy-integration-benefits"}, {"text": "1.2 Development Environment Setup", "url": "#12-development-environment-setup", "type": "anchor", "status": "Anchor found: 1.2 Development Environment Setup → #12-development-environment-setup"}, {"text": "1.2.1 Laravel Sail Configuration", "url": "#121-laravel-sail-configuration", "type": "anchor", "status": "Anchor found: 1.2.1 Laravel Sail Configuration → #121-laravel-sail-configuration"}, {"text": "1.2.2 Environment Configuration", "url": "#122-environment-configuration", "type": "anchor", "status": "Anchor found: 1.2.2 Environment Configuration → #122-environment-configuration"}, {"text": "1.2.3 Taxonomy Development Environment", "url": "#123-taxonomy-development-environment", "type": "anchor", "status": "<PERSON><PERSON> found: 1.2.3 Taxonomy Development Environment → #123-taxonomy-development-environment"}, {"text": "1.3 Debugging Tools", "url": "#13-debugging-tools", "type": "anchor", "status": "Anchor found: 1.3 Debugging Tools → #13-debugging-tools"}, {"text": "1.3.1 Laravel Debugbar Setup", "url": "#131-laravel-debugbar-setup", "type": "anchor", "status": "Anchor found: 1.3.1 <PERSON><PERSON>bugbar Setup → #131-laravel-debugbar-setup"}, {"text": "1.3.2 Debugbar Configuration", "url": "#132-debugbar-configuration", "type": "anchor", "status": "Anchor found: 1.3.2 Debugbar Configuration → #132-debugbar-configuration"}, {"text": "1.3.3 Taxonomy-Aware Debugging", "url": "#133-taxonomy-aware-debugging", "type": "anchor", "status": "An<PERSON> found: 1.3.3 Taxonomy-Aware Debugging → #133-taxonomy-aware-debugging"}, {"text": "1.4 Code Quality Tools", "url": "#14-code-quality-tools", "type": "anchor", "status": "Anchor found: 1.4 Code Quality Tools → #14-code-quality-tools"}, {"text": "1.4.1 Laravel Pint Configuration", "url": "#141-laravel-pint-configuration", "type": "anchor", "status": "Anchor found: 1.4.1 Laravel Pint Configuration → #141-laravel-pint-configuration"}]}, {"file": "packages/development/010-debugbar-guide.md", "total_links": 13, "internal_links": 4, "anchor_links": 9, "external_links": 0, "broken_links": [], "working_links": [{"text": "1.1 Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1 Table of Contents → #11-table-of-contents"}, {"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Laravel Debugbar Setup", "url": "#13-laravel-debugbar-setup", "type": "anchor", "status": "Anchor found: 1.3 <PERSON><PERSON> Setup → #13-laravel-debugbar-setup"}, {"text": "1.4 Laravel Telescope Integration", "url": "#14-laravel-telescope-integration", "type": "anchor", "status": "Anchor found: 1.4 Laravel Telescope Integration → #14-laravel-telescope-integration"}, {"text": "1.5 Ray Debugging Tool", "url": "#15-ray-debugging-tool", "type": "anchor", "status": "Anchor found: 1.5 Ray Debugging Tool → #15-ray-debugging-tool"}, {"text": "1.6 Xdebug Configuration", "url": "#16-xdebug-configuration", "type": "anchor", "status": "Anchor found: 1.6 Xdebug Configuration → #16-xdebug-configuration"}, {"text": "1.7 Custom Debug Helpers", "url": "#17-custom-debug-helpers", "type": "anchor", "status": "Anchor found: 1.7 Custom Debug Helpers → #17-custom-debug-helpers"}, {"text": "1.11 Taxonomy-Specific Debugging", "url": "#111-taxonomy-specific-debugging", "type": "anchor", "status": "Anchor found: 1.11 Taxonomy-Specific Debugging → #111-taxonomy-specific-debugging"}, {"text": "1.12 Best Practices", "url": "#112-best-practices", "type": "anchor", "status": "Anchor found: 1.12 Best Practices → #112-best-practices"}, {"text": "Development Index", "url": "000-development-index.md", "type": "internal", "status": "File exists: packages/development/000-development-index.md"}, {"text": "Pint Code Quality Guide", "url": "020-pint-code-quality-guide.md", "type": "internal", "status": "File exists: packages/development/020-pint-code-quality-guide.md"}, {"text": "Packages Index", "url": "../000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}, {"text": "Chinook Documentation", "url": "../../README.md", "type": "internal", "status": "File exists: README.md"}]}, {"file": "packages/development/020-pint-code-quality-guide.md", "total_links": 13, "internal_links": 4, "anchor_links": 9, "external_links": 0, "broken_links": [], "working_links": [{"text": "2.1 Table of Contents", "url": "#21-table-of-contents", "type": "anchor", "status": "Anchor found: 2.1 Table of Contents → #21-table-of-contents"}, {"text": "2.2 Overview", "url": "#22-overview", "type": "anchor", "status": "Anchor found: 2.2 Overview → #22-overview"}, {"text": "2.3 <PERSON><PERSON>", "url": "#23-laravel-pint-setup", "type": "anchor", "status": "Anchor found: 2.3 <PERSON><PERSON> → #23-laravel-pint-setup"}, {"text": "2.4 PHPStan Configuration", "url": "#24-phpstan-configuration", "type": "anchor", "status": "Anchor found: 2.4 PHPStan Configuration → #24-phpstan-configuration"}, {"text": "2.5 PHP CS Fixer Integration", "url": "#25-php-cs-fixer-integration", "type": "anchor", "status": "Anchor found: 2.5 PHP CS Fixer Integration → #25-php-cs-fixer-integration"}, {"text": "2.6 Code Quality Automation", "url": "#26-code-quality-automation", "type": "anchor", "status": "Anchor found: 2.6 Code Quality Automation → #26-code-quality-automation"}, {"text": "2.7 Pre-commit Hooks", "url": "#27-pre-commit-hooks", "type": "anchor", "status": "Anchor found: 2.7 Pre-commit Hooks → #27-pre-commit-hooks"}, {"text": "2.9 Taxonomy-Specific Quality Rules", "url": "#29-taxonomy-specific-quality-rules", "type": "anchor", "status": "Anchor found: 2.9 Taxonomy-Specific Quality Rules → #29-taxonomy-specific-quality-rules"}, {"text": "2.11 Best Practices", "url": "#211-best-practices", "type": "anchor", "status": "Anchor found: 2.11 Best Practices → #211-best-practices"}, {"text": "Debugbar Guide", "url": "010-debugbar-guide.md", "type": "internal", "status": "File exists: packages/development/010-debugbar-guide.md"}, {"text": "Development Index", "url": "000-development-index.md", "type": "internal", "status": "File exists: packages/development/000-development-index.md"}, {"text": "Packages Index", "url": "../000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}, {"text": "Chinook Documentation", "url": "../../README.md", "type": "internal", "status": "File exists: README.md"}]}, {"file": "packages/testing/000-testing-index.md", "total_links": 22, "internal_links": 5, "anchor_links": 11, "external_links": 6, "broken_links": [], "working_links": [{"text": "1.1 Table of Contents", "url": "#11-table-of-contents", "type": "anchor", "status": "Anchor found: 1.1 Table of Contents → #11-table-of-contents"}, {"text": "1.2 Overview", "url": "#12-overview", "type": "anchor", "status": "Anchor found: 1.2 Overview → #12-overview"}, {"text": "1.3 Testing Philosophy", "url": "#13-testing-philosophy", "type": "anchor", "status": "<PERSON><PERSON> found: 1.3 Testing Philosophy → #13-testing-philosophy"}, {"text": "1.4 Testing Tools & Frameworks", "url": "#14-testing-tools--frameworks", "type": "anchor", "status": "An<PERSON> found: 1.4 Testing Tools & Frameworks → #14-testing-tools--frameworks"}, {"text": "1.5 Taxonomy Testing Architecture", "url": "#15-taxonomy-testing-architecture", "type": "anchor", "status": "<PERSON><PERSON> found: 1.5 Taxonomy Testing Architecture → #15-taxonomy-testing-architecture"}, {"text": "1.6 Testing Best Practices", "url": "#16-testing-best-practices", "type": "anchor", "status": "Anchor found: 1.6 Testing Best Practices → #16-testing-best-practices"}, {"text": "1.7 Testing Workflow", "url": "#17-testing-workflow", "type": "anchor", "status": "Anchor found: 1.7 Testing Workflow → #17-testing-workflow"}, {"text": "1.8 Testing Environment Setup", "url": "#18-testing-environment-setup", "type": "anchor", "status": "Anchor found: 1.8 Testing Environment Setup → #18-testing-environment-setup"}, {"text": "1.9 Taxonomy-Specific Testing Strategies", "url": "#19-taxonomy-specific-testing-strategies", "type": "anchor", "status": "<PERSON><PERSON> found: 1.9 Taxonomy-Specific Testing Strategies → #19-taxonomy-specific-testing-strategies"}, {"text": "1.10 Integration with Development Tools", "url": "#110-integration-with-development-tools", "type": "anchor", "status": "<PERSON><PERSON> found: 1.10 Integration with Development Tools → #110-integration-with-development-tools"}, {"text": "1.11 Resources and References", "url": "#111-resources-and-references", "type": "anchor", "status": "An<PERSON> found: 1.11 Resources and References → #111-resources-and-references"}, {"text": "Pest Testing Guide", "url": "010-pest-testing-guide.md", "type": "internal", "status": "File exists: packages/testing/010-pest-testing-guide.md"}, {"text": "Pest PHP Documentation", "url": "https://pestphp.com/", "type": "external", "status": "External link (not validated)"}, {"text": "Laravel Testing Documentation", "url": "https://laravel.com/docs/testing", "type": "external", "status": "External link (not validated)"}, {"text": "aliziodev/laravel-taxonomy Documentation", "url": "https://github.com/aliziodev/laravel-taxonomy", "type": "external", "status": "External link (not validated)"}, {"text": "Laravel Testing Examples", "url": "https://github.com/laravel/framework/tree/master/tests", "type": "external", "status": "External link (not validated)"}, {"text": "Pest Plugin Ecosystem", "url": "https://pestphp.com/docs/plugins", "type": "external", "status": "External link (not validated)"}, {"text": "Taxonomy Testing Patterns", "url": "https://github.com/aliziodev/laravel-taxonomy/wiki/testing", "type": "external", "status": "External link (not validated)"}, {"text": "Development Index", "url": "../development/000-development-index.md", "type": "internal", "status": "File exists: packages/development/000-development-index.md"}, {"text": "Pest Testing Guide", "url": "010-pest-testing-guide.md", "type": "internal", "status": "File exists: packages/testing/010-pest-testing-guide.md"}, {"text": "Packages Index", "url": "../000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}, {"text": "Chinook Documentation", "url": "../../README.md", "type": "internal", "status": "File exists: README.md"}]}, {"file": "packages/testing/010-pest-testing-guide.md", "total_links": 13, "internal_links": 4, "anchor_links": 9, "external_links": 0, "broken_links": [], "working_links": [{"text": "2.1 Table of Contents", "url": "#21-table-of-contents", "type": "anchor", "status": "Anchor found: 2.1 Table of Contents → #21-table-of-contents"}, {"text": "2.2 Overview", "url": "#22-overview", "type": "anchor", "status": "Anchor found: 2.2 Overview → #22-overview"}, {"text": "2.3 Installation & Configuration", "url": "#23-installation--configuration", "type": "anchor", "status": "Anchor found: 2.3 Installation & Configuration → #23-installation--configuration"}, {"text": "2.4 Taxonomy Test Architecture", "url": "#24-taxonomy-test-architecture", "type": "anchor", "status": "An<PERSON> found: 2.4 Taxonomy Test Architecture → #24-taxonomy-test-architecture"}, {"text": "2.5 Plugin Ecosystem", "url": "#25-plugin-ecosystem", "type": "anchor", "status": "Anchor found: 2.5 Plugin Ecosystem → #25-plugin-ecosystem"}, {"text": "2.6 Advanced Testing Patterns", "url": "#26-advanced-testing-patterns", "type": "anchor", "status": "Anchor found: 2.6 Advanced Testing Patterns → #26-advanced-testing-patterns"}, {"text": "2.7 Taxonomy-Specific Testing", "url": "#27-taxonomy-specific-testing", "type": "anchor", "status": "<PERSON><PERSON> found: 2.7 Taxonomy-Specific Testing → #27-taxonomy-specific-testing"}, {"text": "2.8 Performance Testing", "url": "#28-performance-testing", "type": "anchor", "status": "Anchor found: 2.8 Performance Testing → #28-performance-testing"}, {"text": "2.12 Best Practices", "url": "#212-best-practices", "type": "anchor", "status": "Anchor found: 2.12 Best Practices → #212-best-practices"}, {"text": "Testing Index", "url": "000-testing-index.md", "type": "internal", "status": "File exists: packages/testing/000-testing-index.md"}, {"text": "Development Index", "url": "../development/000-development-index.md", "type": "internal", "status": "File exists: packages/development/000-development-index.md"}, {"text": "Packages Index", "url": "../000-packages-index.md", "type": "internal", "status": "File exists: packages/000-packages-index.md"}, {"text": "Chinook Documentation", "url": "../../README.md", "type": "internal", "status": "File exists: README.md"}]}, {"file": "performance/000-performance-index.md", "total_links": 13, "internal_links": 4, "anchor_links": 9, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Performance Goals", "url": "#2-performance-goals", "type": "anchor", "status": "Anchor found: 2. Performance Goals → #2-performance-goals"}, {"text": "3. Performance Guides", "url": "#3-performance-guides", "type": "anchor", "status": "Anchor found: 3. Performance Guides → #3-performance-guides"}, {"text": "4. Optimization Strategies", "url": "#4-optimization-strategies", "type": "anchor", "status": "<PERSON><PERSON> found: 4. Optimization Strategies → #4-optimization-strategies"}, {"text": "5. Performance Benchmarks", "url": "#5-performance-benchmarks", "type": "anchor", "status": "Anchor found: 5. Performance Benchmarks → #5-performance-benchmarks"}, {"text": "6. Scalability Testing", "url": "#6-scalability-testing", "type": "anchor", "status": "<PERSON><PERSON> found: 6. Scalability Testing → #6-scalability-testing"}, {"text": "7. Best Practices", "url": "#7-best-practices", "type": "anchor", "status": "Anchor found: 7. Best Practices → #7-best-practices"}, {"text": "8. Performance Monitoring", "url": "#8-performance-monitoring", "type": "anchor", "status": "Anchor found: 8. Performance Monitoring → #8-performance-monitoring"}, {"text": "9. <PERSON>", "url": "#9-navigation", "type": "anchor", "status": "Anchor found: 9. <PERSON> → #9-navigation"}, {"text": "Single Taxonomy System Optimization", "url": "100-single-taxonomy-optimization.md", "type": "internal", "status": "File exists: performance/100-single-taxonomy-optimization.md"}, {"text": "Hierarchical Data Caching", "url": "110-hierarchical-data-caching.md", "type": "internal", "status": "File exists: performance/110-hierarchical-data-caching.md"}, {"text": "Testing Index", "url": "../testing/000-testing-index.md", "type": "internal", "status": "File exists: testing/000-testing-index.md"}, {"text": "Single Taxonomy System Optimization", "url": "100-single-taxonomy-optimization.md", "type": "internal", "status": "File exists: performance/100-single-taxonomy-optimization.md"}]}, {"file": "performance/100-single-taxonomy-optimization.md", "total_links": 11, "internal_links": 2, "anchor_links": 9, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Query Optimization Strategies", "url": "#2-query-optimization-strategies", "type": "anchor", "status": "Anchor found: 2. Query Optimization Strategies → #2-query-optimization-strategies"}, {"text": "3. Database Indexing", "url": "#3-database-indexing", "type": "anchor", "status": "Anchor found: 3. Database Indexing → #3-database-indexing"}, {"text": "4. <PERSON><PERSON>oquent Optimization", "url": "#4-laravel-eloquent-optimization", "type": "anchor", "status": "Anchor found: 4. <PERSON><PERSON> Eloquent Optimization → #4-laravel-eloquent-optimization"}, {"text": "5. Caching Strategies", "url": "#5-caching-strategies", "type": "anchor", "status": "Anchor found: 5. Caching Strategies → #5-caching-strategies"}, {"text": "6. Performance Monitoring", "url": "#6-performance-monitoring", "type": "anchor", "status": "Anchor found: 6. Performance Monitoring → #6-performance-monitoring"}, {"text": "7. Benchmarking Results", "url": "#7-benchmarking-results", "type": "anchor", "status": "Anchor found: 7. Benchmarking Results → #7-benchmarking-results"}, {"text": "8. Best Practices", "url": "#8-best-practices", "type": "anchor", "status": "Anchor found: 8. Best Practices → #8-best-practices"}, {"text": "9. <PERSON>", "url": "#9-navigation", "type": "anchor", "status": "Anchor found: 9. <PERSON> → #9-navigation"}, {"text": "Performance Index", "url": "000-performance-index.md", "type": "internal", "status": "File exists: performance/000-performance-index.md"}, {"text": "Hierarchical Data Caching", "url": "110-hierarchical-data-caching.md", "type": "internal", "status": "File exists: performance/110-hierarchical-data-caching.md"}]}, {"file": "performance/110-hierarchical-data-caching.md", "total_links": 11, "internal_links": 2, "anchor_links": 9, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Caching Architecture", "url": "#2-caching-architecture", "type": "anchor", "status": "Anchor found: 2. Caching Architecture → #2-caching-architecture"}, {"text": "3. Taxonomy Hierarchy Caching", "url": "#3-taxonomy-hierarchy-caching", "type": "anchor", "status": "Anchor found: 3. Taxonomy Hierarchy Caching → #3-taxonomy-hierarchy-caching"}, {"text": "4. <PERSON><PERSON> Result <PERSON>ng", "url": "#4-query-result-caching", "type": "anchor", "status": "Anchor found: 4. Query Result Caching → #4-query-result-caching"}, {"text": "5. Cache Invalidation Strategies", "url": "#5-cache-invalidation-strategies", "type": "anchor", "status": "An<PERSON> found: 5. Cache Invalidation Strategies → #5-cache-invalidation-strategies"}, {"text": "6. Performance Optimization", "url": "#6-performance-optimization", "type": "anchor", "status": "Anchor found: 6. Performance Optimization → #6-performance-optimization"}, {"text": "7. Monitoring and Metrics", "url": "#7-monitoring-and-metrics", "type": "anchor", "status": "An<PERSON> found: 7. Monitoring and Metrics → #7-monitoring-and-metrics"}, {"text": "8. Best Practices", "url": "#8-best-practices", "type": "anchor", "status": "Anchor found: 8. Best Practices → #8-best-practices"}, {"text": "9. <PERSON>", "url": "#9-navigation", "type": "anchor", "status": "Anchor found: 9. <PERSON> → #9-navigation"}, {"text": "Single Taxonomy System Optimization", "url": "100-single-taxonomy-optimization.md", "type": "internal", "status": "File exists: performance/100-single-taxonomy-optimization.md"}, {"text": "Performance Index", "url": "000-performance-index.md", "type": "internal", "status": "File exists: performance/000-performance-index.md"}]}, {"file": "testing/000-testing-index.md", "total_links": 22, "internal_links": 11, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. Documentation Structure", "url": "#2-documentation-structure", "type": "anchor", "status": "Anchor found: 2. Documentation Structure → #2-documentation-structure"}, {"text": "2.1 Core Testing Documentation", "url": "#21-core-testing-documentation", "type": "anchor", "status": "Anchor found: 2.1 Core Testing Documentation → #21-core-testing-documentation"}, {"text": "2.2 Specialized Testing Areas", "url": "#22-specialized-testing-areas", "type": "anchor", "status": "Anchor found: 2.2 Specialized Testing Areas → #22-specialized-testing-areas"}, {"text": "3. Testing Philosophy", "url": "#3-testing-philosophy", "type": "anchor", "status": "<PERSON><PERSON> found: 3. Testing Philosophy → #3-testing-philosophy"}, {"text": "4. Quick Start", "url": "#4-quick-start", "type": "anchor", "status": "Anchor found: 4. Quick Start → #4-quick-start"}, {"text": "4.1 Prerequisites", "url": "#41-prerequisites", "type": "anchor", "status": "Anchor found: 4.1 Prerequisites → #41-prerequisites"}, {"text": "4.2 Test Structure", "url": "#42-test-structure", "type": "anchor", "status": "Anchor found: 4.2 Test Structure → #42-test-structure"}, {"text": "5. Testing Standards", "url": "#5-testing-standards", "type": "anchor", "status": "An<PERSON> found: 5. Testing Standards → #5-testing-standards"}, {"text": "6. Related Documentation", "url": "#6-related-documentation", "type": "anchor", "status": "Anchor found: 6. Related Documentation → #6-related-documentation"}, {"text": "7. <PERSON>", "url": "#7-navigation", "type": "anchor", "status": "Anchor found: 7. <PERSON> → #7-navigation"}, {"text": "Index Overview", "url": "index/000-index-overview.md", "type": "internal", "status": "File exists: testing/index/000-index-overview.md"}, {"text": "Trait Testing Guide", "url": "070-trait-testing-guide.md", "type": "internal", "status": "File exists: testing/070-trait-testing-guide.md"}, {"text": "Diagrams Index", "url": "diagrams/000-diagrams-index.md", "type": "internal", "status": "File exists: testing/diagrams/000-diagrams-index.md"}, {"text": "Quality Index", "url": "quality/000-quality-index.md", "type": "internal", "status": "File exists: testing/quality/000-quality-index.md"}, {"text": "Chinook Models Guide", "url": "../010-chinook-models-guide.md", "type": "internal", "status": "File exists: 010-chinook-models-guide.md"}, {"text": "Chinook Migrations Guide", "url": "../020-chinook-migrations-guide.md", "type": "internal", "status": "File exists: 020-chinook-migrations-guide.md"}, {"text": "Chinook Factories Guide", "url": "../030-chinook-factories-guide.md", "type": "internal", "status": "File exists: 030-chinook-factories-guide.md"}, {"text": "Chinook Seeders Guide", "url": "../040-chinook-seeders-guide.md", "type": "internal", "status": "File exists: 040-chinook-seeders-guide.md"}, {"text": "aliziodev/laravel-taxonomy Guide", "url": "../packages/110-aliziodev-laravel-taxonomy-guide.md", "type": "internal", "status": "File exists: packages/110-aliziodev-laravel-taxonomy-guide.md"}, {"text": "Chinook Index", "url": "../000-chinook-index.md", "type": "internal", "status": "File exists: 000-chinook-index.md"}, {"text": "Index Overview", "url": "index/000-index-overview.md", "type": "internal", "status": "File exists: testing/index/000-index-overview.md"}]}, {"file": "testing/070-trait-testing-guide.md", "total_links": 11, "internal_links": 2, "anchor_links": 9, "external_links": 0, "broken_links": [{"text": "RBAC Testing Guide", "url": "060-rbac-testing-guide.md", "type": "internal", "status": "File not found: testing/060-rbac-testing-guide.md"}, {"text": "Hierarchical Data Testing", "url": "080-hierarchical-data-testing.md", "type": "internal", "status": "File not found: testing/080-hierarchical-data-testing.md"}], "working_links": [{"text": "1. Overview", "url": "#1-overview", "type": "anchor", "status": "Anchor found: 1. Overview → #1-overview"}, {"text": "2. HasSecondaryUniqueKey Trait Testing", "url": "#2-hassecondaryuniquekey-trait-testing", "type": "anchor", "status": "Anchor found: 2. HasSecondaryUniqueKey Trait Testing → #2-hassecondaryuniquekey-trait-testing"}, {"text": "3. <PERSON><PERSON><PERSON> Trait Testing", "url": "#3-hasslug-trait-testing", "type": "anchor", "status": "Anchor found: 3. Has<PERSON><PERSON> Trait Testing → #3-hasslug-trait-testing"}, {"text": "4. HasTaxonomies Trait Testing", "url": "#4-hastaxonomies-trait-testing", "type": "anchor", "status": "Anchor found: 4. HasTaxonomies Trait Testing → #4-hastaxonomies-trait-testing"}, {"text": "5. Userstamps Trait Testing", "url": "#5-userstamps-trait-testing", "type": "anchor", "status": "Anchor found: 5. Userstamps Trait Testing → #5-userstamps-trait-testing"}, {"text": "6. SoftDeletes Trait Testing", "url": "#6-softdeletes-trait-testing", "type": "anchor", "status": "Anchor found: 6. SoftDeletes Trait Testing → #6-softdeletes-trait-testing"}, {"text": "7. Trait Interaction Testing", "url": "#7-trait-interaction-testing", "type": "anchor", "status": "An<PERSON> found: 7. <PERSON><PERSON>t Interaction Testing → #7-trait-interaction-testing"}, {"text": "8. Best Practices", "url": "#8-best-practices", "type": "anchor", "status": "Anchor found: 8. Best Practices → #8-best-practices"}, {"text": "9. <PERSON>", "url": "#9-navigation", "type": "anchor", "status": "Anchor found: 9. <PERSON> → #9-navigation"}]}, {"file": "testing/diagrams/000-diagrams-index.md", "total_links": 14, "internal_links": 3, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Testing Diagrams Index", "url": "#1-testing-diagrams-index", "type": "anchor", "status": "<PERSON><PERSON> found: 1. Testing Diagrams Index → #1-testing-diagrams-index"}, {"text": "1.1 Overview", "url": "#11-overview", "type": "anchor", "status": "Anchor found: 1.1 Overview → #11-overview"}, {"text": "1.2 Table of Contents", "url": "#12-table-of-contents", "type": "anchor", "status": "Anchor found: 1.2 Table of Contents → #12-table-of-contents"}, {"text": "1.3 Testing Architecture Diagrams", "url": "#13-testing-architecture-diagrams", "type": "anchor", "status": "An<PERSON> found: 1.3 Testing Architecture Diagrams → #13-testing-architecture-diagrams"}, {"text": "1.4 Test Flow Diagrams", "url": "#14-test-flow-diagrams", "type": "anchor", "status": "Anchor found: 1.4 Test Flow Diagrams → #14-test-flow-diagrams"}, {"text": "1.5 Quality Assurance Workflows", "url": "#15-quality-assurance-workflows", "type": "anchor", "status": "Anchor found: 1.5 Quality Assurance Workflows → #15-quality-assurance-workflows"}, {"text": "1.6 Taxonomy Testing Visualizations", "url": "#16-taxonomy-testing-visualizations", "type": "anchor", "status": "<PERSON><PERSON> found: 1.6 Taxonomy Testing Visualizations → #16-taxonomy-testing-visualizations"}, {"text": "1.7 Performance Testing Diagrams", "url": "#17-performance-testing-diagrams", "type": "anchor", "status": "Anchor found: 1.7 Performance Testing Diagrams → #17-performance-testing-diagrams"}, {"text": "1.8 Integration Testing Flows", "url": "#18-integration-testing-flows", "type": "anchor", "status": "<PERSON><PERSON> found: 1.8 Integration Testing Flows → #18-integration-testing-flows"}, {"text": "1.9 Accessibility Compliance", "url": "#19-accessibility-compliance", "type": "anchor", "status": "<PERSON><PERSON> found: 1.9 Accessibility Compliance → #19-accessibility-compliance"}, {"text": "1.10 Navigation", "url": "#110-navigation", "type": "anchor", "status": "Anchor found: 1.10 Navigation → #110-navigation"}, {"text": "Testing Index", "url": "../000-testing-index.md", "type": "internal", "status": "File exists: testing/000-testing-index.md"}, {"text": "Testing Quality Index", "url": "../quality/000-quality-index.md", "type": "internal", "status": "File exists: testing/quality/000-quality-index.md"}, {"text": "Testing Documentation", "url": "../000-testing-index.md", "type": "internal", "status": "File exists: testing/000-testing-index.md"}]}, {"file": "testing/index/000-index-overview.md", "total_links": 14, "internal_links": 3, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Testing Index Overview", "url": "#1-testing-index-overview", "type": "anchor", "status": "Anchor found: 1. Testing Index Overview → #1-testing-index-overview"}, {"text": "1.1 Overview", "url": "#11-overview", "type": "anchor", "status": "Anchor found: 1.1 Overview → #11-overview"}, {"text": "1.2 Table of Contents", "url": "#12-table-of-contents", "type": "anchor", "status": "Anchor found: 1.2 Table of Contents → #12-table-of-contents"}, {"text": "1.3 Testing Documentation Structure", "url": "#13-testing-documentation-structure", "type": "anchor", "status": "Anchor found: 1.3 Testing Documentation Structure → #13-testing-documentation-structure"}, {"text": "1.4 Test Categories", "url": "#14-test-categories", "type": "anchor", "status": "Anchor found: 1.4 Test Categories → #14-test-categories"}, {"text": "1.5 Framework Integration", "url": "#15-framework-integration", "type": "anchor", "status": "<PERSON><PERSON> found: 1.5 Framework Integration → #15-framework-integration"}, {"text": "1.6 Taxonomy Testing Index", "url": "#16-taxonomy-testing-index", "type": "anchor", "status": "<PERSON><PERSON> found: 1.6 Taxonomy Testing Index → #16-taxonomy-testing-index"}, {"text": "1.7 Performance Testing Index", "url": "#17-performance-testing-index", "type": "anchor", "status": "Anchor found: 1.7 Performance Testing Index → #17-performance-testing-index"}, {"text": "1.8 Quality Assurance Index", "url": "#18-quality-assurance-index", "type": "anchor", "status": "Anchor found: 1.8 Quality Assurance Index → #18-quality-assurance-index"}, {"text": "1.9 Test Execution Guides", "url": "#19-test-execution-guides", "type": "anchor", "status": "Anchor found: 1.9 Test Execution Guides → #19-test-execution-guides"}, {"text": "1.10 Navigation", "url": "#110-navigation", "type": "anchor", "status": "Anchor found: 1.10 Navigation → #110-navigation"}, {"text": "Testing Documentation", "url": "../000-testing-index.md", "type": "internal", "status": "File exists: testing/000-testing-index.md"}, {"text": "Quality Assurance Index", "url": "../quality/000-quality-index.md", "type": "internal", "status": "File exists: testing/quality/000-quality-index.md"}, {"text": "Testing Documentation", "url": "../000-testing-index.md", "type": "internal", "status": "File exists: testing/000-testing-index.md"}]}, {"file": "testing/quality/000-quality-index.md", "total_links": 14, "internal_links": 3, "anchor_links": 11, "external_links": 0, "broken_links": [], "working_links": [{"text": "1. Quality Assurance Index", "url": "#1-quality-assurance-index", "type": "anchor", "status": "Anchor found: 1. Quality Assurance Index → #1-quality-assurance-index"}, {"text": "1.1 Overview", "url": "#11-overview", "type": "anchor", "status": "Anchor found: 1.1 Overview → #11-overview"}, {"text": "1.2 Table of Contents", "url": "#12-table-of-contents", "type": "anchor", "status": "Anchor found: 1.2 Table of Contents → #12-table-of-contents"}, {"text": "1.3 Quality Standards", "url": "#13-quality-standards", "type": "anchor", "status": "Anchor found: 1.3 Quality Standards → #13-quality-standards"}, {"text": "1.4 Testing Methodologies", "url": "#14-testing-methodologies", "type": "anchor", "status": "<PERSON><PERSON> found: 1.4 Testing Methodologies → #14-testing-methodologies"}, {"text": "1.5 Code Quality Metrics", "url": "#15-code-quality-metrics", "type": "anchor", "status": "<PERSON><PERSON> found: 1.5 Code Quality Metrics → #15-code-quality-metrics"}, {"text": "1.6 Taxonomy Quality Assurance", "url": "#16-taxonomy-quality-assurance", "type": "anchor", "status": "An<PERSON> found: 1.6 Taxonomy Quality Assurance → #16-taxonomy-quality-assurance"}, {"text": "1.7 Performance Quality Gates", "url": "#17-performance-quality-gates", "type": "anchor", "status": "Anchor found: 1.7 Performance Quality Gates → #17-performance-quality-gates"}, {"text": "1.8 Security Quality Validation", "url": "#18-security-quality-validation", "type": "anchor", "status": "An<PERSON> found: 1.8 Security Quality Validation → #18-security-quality-validation"}, {"text": "1.9 Continuous Quality Monitoring", "url": "#19-continuous-quality-monitoring", "type": "anchor", "status": "Anchor found: 1.9 Continuous Quality Monitoring → #19-continuous-quality-monitoring"}, {"text": "1.10 Navigation", "url": "#110-navigation", "type": "anchor", "status": "Anchor found: 1.10 Navigation → #110-navigation"}, {"text": "Testing Index Overview", "url": "../index/000-index-overview.md", "type": "internal", "status": "File exists: testing/index/000-index-overview.md"}, {"text": "Testing Documentation", "url": "../000-testing-index.md", "type": "internal", "status": "File exists: testing/000-testing-index.md"}, {"text": "Testing Documentation", "url": "../000-testing-index.md", "type": "internal", "status": "File exists: testing/000-testing-index.md"}]}], "total_broken": 26}